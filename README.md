# 青空文庫リーダー

「Yom!青空文庫」アプリにインスパイアされた、青空文庫の作品を快適に読むためのWebアプリケーションです。

## ✨ 主要機能

### 📚 読書機能
- **縦書き/横書き表示**: 日本語の伝統的な縦書き表示に完全対応
- **カスタマイズ可能な表示設定**: フォントサイズ、行間、フォント種類、ページ幅の調整
- **青空文庫テキスト処理**: ルビ、注記、見出しを適切にHTMLに変換
- **読書進捗管理**: 自動的に読書位置を保存し、続きから読める

### ❤️ ユーザー体験
- **お気に入り機能**: 気に入った作品をお気に入りに追加
- **読書履歴**: 最近読んだ作品の履歴を自動保存
- **レビュー・感想機能**: 5段階評価と詳細なレビューを投稿
- **ダークモード**: 目に優しい暗いテーマに対応

### 🌐 ソーシャル・共有
- **読書記録の共有**: Twitter、Facebook、LINEで読書記録を共有
- **Web Share API**: モバイルデバイスでのネイティブ共有機能
- **クリップボード共有**: 読書記録をテキストとしてコピー

### 📱 PWA機能
- **オフライン対応**: Service Workerによるオフライン閲覧
- **アプリインストール**: ホーム画面にアプリとしてインストール可能
- **レスポンシブデザイン**: PC・スマートフォン・タブレットに対応
- **プッシュ通知対応**: 将来的な通知機能の基盤

### ⚙️ 技術的特徴
- **設定の永続化**: すべての設定をlocalStorageに保存
- **モジュラー設計**: 機能ごとに分離された保守しやすいコード構造
- **カスタムフック**: React の最新パターンを使用した状態管理
- **高速な動作**: React 19 + Vite による最適化されたパフォーマンス

## 🏗️ アーキテクチャ

```mermaid
graph TB
    subgraph "フロントエンド (React 19 + Vite)"
        A[App.jsx] --> B[読書画面]
        A --> C[設定画面]
        A --> D[ホーム画面]

        B --> E[ReviewDialog]
        B --> F[ShareDialog]

        A --> G[カスタムフック]
        G --> H[useSettings]
        G --> I[useReadingProgress]
        G --> J[useFavorites]
        G --> K[useReviews]
    end

    subgraph "データ管理"
        L[localStorage] --> M[設定データ]
        L --> N[読書進捗]
        L --> O[お気に入り]
        L --> P[レビューデータ]
        L --> Q[読書履歴]
    end

    subgraph "外部サービス"
        R[青空文庫API] --> S[テキストデータ]
        T[SNS共有] --> U[Twitter/Facebook/LINE]
    end

    subgraph "PWA機能"
        V[Service Worker] --> W[オフライン対応]
        V --> X[キャッシュ管理]
        Y[Manifest] --> Z[アプリインストール]
    end

    G --> L
    B --> R
    F --> T
    A --> V
    A --> Y
```

## 🛠️ 技術スタック

### フロントエンド
- **React 19**: 最新のReactフレームワーク
- **Vite**: 高速なビルドツール
- **Tailwind CSS v4**: ユーティリティファーストのCSSフレームワーク
- **shadcn/ui**: モダンなUIコンポーネントライブラリ
- **Lucide React**: 美しいアイコンライブラリ

### 状態管理・データ
- **localStorage**: クライアントサイドデータ永続化
- **カスタムフック**: React の最新パターンによる状態管理
- **青空文庫テキスト処理**: 独自のパーサーによるテキスト変換

### PWA・デプロイ
- **Service Worker**: オフライン対応とキャッシュ管理
- **Web App Manifest**: PWAインストール対応
- **GitHub Pages**: 静的サイトホスティング
- **GitHub Actions**: CI/CD自動デプロイ

## 🚀 デモ

**ライブデモ**: [https://tomoto0.github.io/aozora-reader/](https://tomoto0.github.io/aozora-reader/)

## 💻 開発

### 前提条件

- Node.js 20以上
- pnpm (推奨) または npm

### セットアップ

```bash
# リポジトリをクローン
git clone https://github.com/tomoto0/aozora-reader.git
cd aozora-reader

# 依存関係をインストール
pnpm install
# または
npm install

# 開発サーバーを起動
pnpm run dev
# または
npm run dev
```

### ビルド

```bash
# プロダクションビルド
pnpm run build

# ビルド結果をプレビュー
pnpm run preview
```

### プロジェクト構造

```
src/
├── components/          # UIコンポーネント
│   ├── ui/             # shadcn/ui コンポーネント
│   ├── ReviewDialog.jsx # レビュー機能
│   └── ShareDialog.jsx  # 共有機能
├── hooks/              # カスタムフック
│   └── useSettings.js  # 設定・データ管理
├── lib/                # ユーティリティ
│   ├── aozora.js       # 青空文庫データ処理
│   ├── storage.js      # ローカルストレージ管理
│   ├── social.js       # ソーシャル共有
│   ├── pwa.js          # PWA機能
│   └── utils.js        # 共通ユーティリティ
├── App.jsx             # メインアプリケーション
├── App.css             # スタイル定義
└── main.jsx            # エントリーポイント
```

## 📋 機能一覧

### ✅ 実装済み機能

#### 基本機能
- ✅ 作品一覧表示（8作品収録）
- ✅ 検索機能（作品名・著者名）
- ✅ レスポンシブデザイン（PC・スマートフォン・タブレット対応）
- ✅ 実際の青空文庫テキストデータの取得・表示

#### 読書機能
- ✅ 縦書き/横書き表示の切り替え
- ✅ フォントサイズ・行間・フォント種類の調整
- ✅ ページ幅の調整（狭い・標準・広い）
- ✅ 読書進捗の自動保存
- ✅ 青空文庫記号の適切な変換（ルビ、注記、見出し等）

#### ユーザー機能
- ✅ お気に入り機能
- ✅ 読書履歴の自動記録
- ✅ レビュー・感想機能（5段階評価）
- ✅ ダークモード切り替え
- ✅ 設定の永続化（localStorage）

#### ソーシャル機能
- ✅ 読書記録の共有（Twitter、Facebook、LINE）
- ✅ Web Share API対応（モバイル）
- ✅ クリップボードへのコピー機能

#### PWA機能
- ✅ Service Worker（オフライン対応）
- ✅ Web App Manifest（アプリインストール）
- ✅ オフライン状態の表示
- ✅ キャッシュ管理

### 🚧 今後の実装予定

- 🔍 全文検索機能
- 🔖 ブックマーク機能
- 🎵 音声読み上げ機能（Web Speech API）
- ☁️ クラウド同期機能
- 📊 詳細な読書統計
- 🌐 多言語対応
- 🧪 テスト環境の構築

## 🤝 コントリビューション

プルリクエストやイシューの報告を歓迎します！

### 開発に参加する

1. このリポジトリをフォーク
2. 機能ブランチを作成 (`git checkout -b feature/amazing-feature`)
3. 変更をコミット (`git commit -m 'Add some amazing feature'`)
4. ブランチにプッシュ (`git push origin feature/amazing-feature`)
5. プルリクエストを作成

## 📄 ライセンス

MIT License - 詳細は [LICENSE](LICENSE) ファイルを参照してください。

## 🙏 謝辞

- このプロジェクトは「Yom!青空文庫」アプリ（HyuCode LLC）にインスパイアされて作成されました
- [青空文庫](https://www.aozora.gr.jp/) - 貴重な文学作品を無料で提供していただいています
- [shadcn/ui](https://ui.shadcn.com/) - 美しいUIコンポーネントライブラリ
- [Tailwind CSS](https://tailwindcss.com/) - 効率的なスタイリングフレームワーク

## 📚 青空文庫について

青空文庫の作品は[青空文庫収録ファイルの取り扱い規準](https://www.aozora.gr.jp/guide/kijyunn.html)に従って利用されています。

## 📞 サポート

質問やサポートが必要な場合は、[GitHub Issues](https://github.com/tomoto0/aozora-reader/issues) でお気軽にお問い合わせください。


// Service Worker for 青空文庫リーダー
const CACHE_NAME = 'aozora-reader-v1.0.0';
const STATIC_CACHE_NAME = 'aozora-reader-static-v1.0.0';
const DYNAMIC_CACHE_NAME = 'aozora-reader-dynamic-v1.0.0';

// キャッシュするリソース
const STATIC_ASSETS = [
  '/aozora-reader/',
  '/aozora-reader/index.html',
  '/aozora-reader/favicon.ico',
  '/aozora-reader/manifest.json'
];

// インストール時の処理
self.addEventListener('install', (event) => {
  console.log('Service Worker: Installing...');
  
  event.waitUntil(
    caches.open(STATIC_CACHE_NAME)
      .then((cache) => {
        console.log('Service Worker: Caching static assets');
        return cache.addAll(STATIC_ASSETS);
      })
      .then(() => {
        console.log('Service Worker: Installation complete');
        return self.skipWaiting();
      })
      .catch((error) => {
        console.error('Service Worker: Installation failed', error);
      })
  );
});

// アクティベート時の処理
self.addEventListener('activate', (event) => {
  console.log('Service Worker: Activating...');
  
  event.waitUntil(
    caches.keys()
      .then((cacheNames) => {
        return Promise.all(
          cacheNames.map((cacheName) => {
            if (cacheName !== STATIC_CACHE_NAME && cacheName !== DYNAMIC_CACHE_NAME) {
              console.log('Service Worker: Deleting old cache', cacheName);
              return caches.delete(cacheName);
            }
          })
        );
      })
      .then(() => {
        console.log('Service Worker: Activation complete');
        return self.clients.claim();
      })
  );
});

// フェッチ時の処理
self.addEventListener('fetch', (event) => {
  const { request } = event;
  const url = new URL(request.url);
  
  // 同一オリジンのリクエストのみ処理
  if (url.origin !== location.origin) {
    return;
  }
  
  // ナビゲーションリクエストの処理
  if (request.mode === 'navigate') {
    event.respondWith(
      caches.match('/aozora-reader/index.html')
        .then((response) => {
          return response || fetch(request);
        })
        .catch(() => {
          return caches.match('/aozora-reader/index.html');
        })
    );
    return;
  }
  
  // その他のリクエストの処理
  event.respondWith(
    caches.match(request)
      .then((response) => {
        if (response) {
          console.log('Service Worker: Serving from cache', request.url);
          return response;
        }
        
        return fetch(request)
          .then((fetchResponse) => {
            // レスポンスが有効でない場合はそのまま返す
            if (!fetchResponse || fetchResponse.status !== 200 || fetchResponse.type !== 'basic') {
              return fetchResponse;
            }
            
            // 動的キャッシュに保存
            const responseToCache = fetchResponse.clone();
            caches.open(DYNAMIC_CACHE_NAME)
              .then((cache) => {
                cache.put(request, responseToCache);
              });
            
            return fetchResponse;
          })
          .catch((error) => {
            console.error('Service Worker: Fetch failed', error);
            
            // オフライン時のフォールバック
            if (request.destination === 'document') {
              return caches.match('/aozora-reader/index.html');
            }
            
            throw error;
          });
      })
  );
});

// バックグラウンド同期
self.addEventListener('sync', (event) => {
  console.log('Service Worker: Background sync', event.tag);
  
  if (event.tag === 'background-sync') {
    event.waitUntil(
      // バックグラウンドでの同期処理
      syncData()
    );
  }
});

// プッシュ通知
self.addEventListener('push', (event) => {
  console.log('Service Worker: Push received');
  
  const options = {
    body: event.data ? event.data.text() : '新しい通知があります',
    icon: '/aozora-reader/icon-192.png',
    badge: '/aozora-reader/icon-192.png',
    vibrate: [100, 50, 100],
    data: {
      dateOfArrival: Date.now(),
      primaryKey: 1
    },
    actions: [
      {
        action: 'explore',
        title: '開く',
        icon: '/aozora-reader/icon-192.png'
      },
      {
        action: 'close',
        title: '閉じる',
        icon: '/aozora-reader/icon-192.png'
      }
    ]
  };
  
  event.waitUntil(
    self.registration.showNotification('青空文庫リーダー', options)
  );
});

// 通知クリック時の処理
self.addEventListener('notificationclick', (event) => {
  console.log('Service Worker: Notification click received');
  
  event.notification.close();
  
  if (event.action === 'explore') {
    event.waitUntil(
      clients.openWindow('/aozora-reader/')
    );
  }
});

// メッセージ受信時の処理
self.addEventListener('message', (event) => {
  console.log('Service Worker: Message received', event.data);
  
  if (event.data && event.data.type === 'SKIP_WAITING') {
    self.skipWaiting();
  }
  
  if (event.data && event.data.type === 'GET_VERSION') {
    event.ports[0].postMessage({ version: CACHE_NAME });
  }
});

// データ同期関数
async function syncData() {
  try {
    console.log('Service Worker: Syncing data...');
    
    // ここで必要に応じてデータの同期処理を実装
    // 例: 読書進捗の同期、新しい作品の取得など
    
    console.log('Service Worker: Data sync complete');
  } catch (error) {
    console.error('Service Worker: Data sync failed', error);
  }
}

// キャッシュサイズの管理
async function limitCacheSize(cacheName, maxItems) {
  const cache = await caches.open(cacheName);
  const keys = await cache.keys();
  
  if (keys.length > maxItems) {
    const keysToDelete = keys.slice(0, keys.length - maxItems);
    await Promise.all(keysToDelete.map(key => cache.delete(key)));
  }
}

// 定期的なキャッシュクリーンアップ
setInterval(() => {
  limitCacheSize(DYNAMIC_CACHE_NAME, 50);
}, 60000); // 1分ごと

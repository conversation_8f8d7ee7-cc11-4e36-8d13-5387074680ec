<!doctype html>
<html lang="ja">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/x-icon" href="/favicon.ico" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>青空文庫リーダー</title>

    <!-- PWA Manifest -->
    <link rel="manifest" href="/manifest.json" />

    <!-- PWA Meta Tags -->
    <meta name="theme-color" content="#000000" />
    <meta name="background-color" content="#ffffff" />
    <meta name="display" content="standalone" />
    <meta name="orientation" content="any" />

    <!-- iOS PWA Support -->
    <meta name="apple-mobile-web-app-capable" content="yes" />
    <meta name="apple-mobile-web-app-status-bar-style" content="default" />
    <meta name="apple-mobile-web-app-title" content="青空文庫" />
    <link rel="apple-touch-icon" href="/icon-192.png" />

    <!-- Android PWA Support -->
    <meta name="mobile-web-app-capable" content="yes" />

    <!-- SEO Meta Tags -->
    <meta name="description" content="青空文庫の作品を快適に読むためのWebアプリケーション。縦書き表示、読書進捗保存、お気に入り機能など充実した機能を提供します。" />
    <meta name="keywords" content="青空文庫,読書,電子書籍,日本文学,縦書き,PWA" />
    <meta name="author" content="青空文庫リーダー" />

    <!-- Open Graph Meta Tags -->
    <meta property="og:title" content="青空文庫リーダー" />
    <meta property="og:description" content="青空文庫の作品を快適に読むためのWebアプリケーション" />
    <meta property="og:type" content="website" />
    <meta property="og:url" content="https://your-domain.github.io/aozora-reader/" />
    <meta property="og:image" content="https://your-domain.github.io/aozora-reader/icon-512.png" />

    <!-- Twitter Card Meta Tags -->
    <meta name="twitter:card" content="summary_large_image" />
    <meta name="twitter:title" content="青空文庫リーダー" />
    <meta name="twitter:description" content="青空文庫の作品を快適に読むためのWebアプリケーション" />
    <meta name="twitter:image" content="https://your-domain.github.io/aozora-reader/icon-512.png" />
  </head>
  <body>
    <div id="root"></div>
    <script type="module" src="/src/main.jsx"></script>
  </body>
</html>

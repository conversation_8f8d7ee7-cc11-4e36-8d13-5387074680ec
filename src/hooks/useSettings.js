import { useState, useEffect } from 'react';
import { SettingsManager } from '@/lib/storage.js';

/**
 * 設定管理のカスタムフック
 */
export function useSettings() {
  const [settings, setSettings] = useState(() => SettingsManager.getSettings());
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);

  // 設定を更新する関数
  const updateSettings = async (newSettings) => {
    setIsLoading(true);
    setError(null);
    
    try {
      const updatedSettings = SettingsManager.saveSettings(newSettings);
      setSettings(updatedSettings);
      
      // CSSカスタムプロパティを更新
      updateCSSVariables(updatedSettings);
      
      return updatedSettings;
    } catch (err) {
      setError(err.message);
      throw err;
    } finally {
      setIsLoading(false);
    }
  };

  // 設定をリセットする関数
  const resetSettings = async () => {
    setIsLoading(true);
    setError(null);
    
    try {
      const defaultSettings = SettingsManager.resetSettings();
      setSettings(defaultSettings);
      
      // CSSカスタムプロパティを更新
      updateCSSVariables(defaultSettings);
      
      return defaultSettings;
    } catch (err) {
      setError(err.message);
      throw err;
    } finally {
      setIsLoading(false);
    }
  };

  // 個別の設定を更新する便利関数
  const updateSetting = (key, value) => {
    return updateSettings({ [key]: value });
  };

  // CSSカスタムプロパティを更新する関数
  const updateCSSVariables = (settings) => {
    const root = document.documentElement;
    
    // フォントサイズ
    root.style.setProperty('--font-size', `${settings.fontSize}px`);
    
    // 行間
    root.style.setProperty('--line-height', settings.lineHeight.toString());
    
    // フォントファミリー
    const fontFamilyMap = {
      serif: '"Noto Serif JP", "Yu Mincho", "YuMincho", "Hiragino Mincho Pro", serif',
      'sans-serif': '"Noto Sans JP", "Yu Gothic", "YuGothic", "Hiragino Sans", sans-serif',
      monospace: '"Noto Sans Mono CJK JP", "Yu Gothic", monospace'
    };
    root.style.setProperty('--font-family', fontFamilyMap[settings.fontFamily] || fontFamilyMap.serif);
    
    // ページ幅
    const pageWidthMap = {
      narrow: '600px',
      normal: '800px',
      wide: '1000px'
    };
    root.style.setProperty('--page-width', pageWidthMap[settings.pageWidth] || pageWidthMap.normal);
    
    // ダークモード
    if (settings.darkMode) {
      document.documentElement.classList.add('dark');
    } else {
      document.documentElement.classList.remove('dark');
    }
  };

  // 初期化時にCSSを適用
  useEffect(() => {
    updateCSSVariables(settings);
  }, []);

  return {
    settings,
    updateSettings,
    updateSetting,
    resetSettings,
    isLoading,
    error
  };
}

/**
 * 読書進捗管理のカスタムフック
 */
import { ReadingProgressManager } from '@/lib/storage.js';

export function useReadingProgress(bookId) {
  const [progress, setProgress] = useState(() => 
    bookId ? ReadingProgressManager.getProgress(bookId) : null
  );
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);

  // 進捗を更新する関数
  const updateProgress = async (newProgress) => {
    if (!bookId) return;
    
    setIsLoading(true);
    setError(null);
    
    try {
      const updatedProgress = ReadingProgressManager.saveProgress(bookId, newProgress);
      setProgress(updatedProgress);
      return updatedProgress;
    } catch (err) {
      setError(err.message);
      throw err;
    } finally {
      setIsLoading(false);
    }
  };

  // 進捗を削除する関数
  const deleteProgress = async () => {
    if (!bookId) return;
    
    setIsLoading(true);
    setError(null);
    
    try {
      ReadingProgressManager.deleteProgress(bookId);
      setProgress(ReadingProgressManager.getProgress(bookId));
    } catch (err) {
      setError(err.message);
      throw err;
    } finally {
      setIsLoading(false);
    }
  };

  // bookIdが変更された時に進捗を再読み込み
  useEffect(() => {
    if (bookId) {
      setProgress(ReadingProgressManager.getProgress(bookId));
    }
  }, [bookId]);

  return {
    progress,
    updateProgress,
    deleteProgress,
    isLoading,
    error
  };
}

/**
 * お気に入り管理のカスタムフック
 */
import { FavoritesManager } from '@/lib/storage.js';

export function useFavorites() {
  const [favorites, setFavorites] = useState(() => FavoritesManager.getFavorites());
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);

  // お気に入りに追加
  const addFavorite = async (bookId) => {
    setIsLoading(true);
    setError(null);
    
    try {
      const updatedFavorites = FavoritesManager.addFavorite(bookId);
      setFavorites(updatedFavorites);
      return updatedFavorites;
    } catch (err) {
      setError(err.message);
      throw err;
    } finally {
      setIsLoading(false);
    }
  };

  // お気に入りから削除
  const removeFavorite = async (bookId) => {
    setIsLoading(true);
    setError(null);
    
    try {
      const updatedFavorites = FavoritesManager.removeFavorite(bookId);
      setFavorites(updatedFavorites);
      return updatedFavorites;
    } catch (err) {
      setError(err.message);
      throw err;
    } finally {
      setIsLoading(false);
    }
  };

  // お気に入り状態をトグル
  const toggleFavorite = async (bookId) => {
    if (FavoritesManager.isFavorite(bookId)) {
      return removeFavorite(bookId);
    } else {
      return addFavorite(bookId);
    }
  };

  // お気に入りかどうかをチェック
  const isFavorite = (bookId) => {
    return favorites.includes(bookId);
  };

  return {
    favorites,
    addFavorite,
    removeFavorite,
    toggleFavorite,
    isFavorite,
    isLoading,
    error
  };
}

/**
 * 読書履歴管理のカスタムフック
 */
import { ReadingHistoryManager } from '@/lib/storage.js';

export function useReadingHistory() {
  const [history, setHistory] = useState(() => ReadingHistoryManager.getHistory());
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);

  // 履歴に追加
  const addToHistory = async (bookId, bookInfo) => {
    setIsLoading(true);
    setError(null);

    try {
      const updatedHistory = ReadingHistoryManager.addToHistory(bookId, bookInfo);
      setHistory(updatedHistory);
      return updatedHistory;
    } catch (err) {
      setError(err.message);
      throw err;
    } finally {
      setIsLoading(false);
    }
  };

  // 履歴をクリア
  const clearHistory = async () => {
    setIsLoading(true);
    setError(null);

    try {
      const clearedHistory = ReadingHistoryManager.clearHistory();
      setHistory(clearedHistory);
      return clearedHistory;
    } catch (err) {
      setError(err.message);
      throw err;
    } finally {
      setIsLoading(false);
    }
  };

  return {
    history,
    addToHistory,
    clearHistory,
    isLoading,
    error
  };
}

/**
 * レビュー管理のカスタムフック
 */
import { ReviewManager } from '@/lib/storage.js';

export function useReviews(bookId) {
  const [reviews, setReviews] = useState(() =>
    bookId ? ReviewManager.getReviews(bookId) : []
  );
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);

  // レビューを追加
  const addReview = async (review) => {
    if (!bookId) return;

    setIsLoading(true);
    setError(null);

    try {
      const newReview = ReviewManager.addReview(bookId, review);
      setReviews(ReviewManager.getReviews(bookId));
      return newReview;
    } catch (err) {
      setError(err.message);
      throw err;
    } finally {
      setIsLoading(false);
    }
  };

  // レビューを更新
  const updateReview = async (reviewId, updates) => {
    if (!bookId) return;

    setIsLoading(true);
    setError(null);

    try {
      const updatedReview = ReviewManager.updateReview(bookId, reviewId, updates);
      setReviews(ReviewManager.getReviews(bookId));
      return updatedReview;
    } catch (err) {
      setError(err.message);
      throw err;
    } finally {
      setIsLoading(false);
    }
  };

  // レビューを削除
  const deleteReview = async (reviewId) => {
    if (!bookId) return;

    setIsLoading(true);
    setError(null);

    try {
      ReviewManager.deleteReview(bookId, reviewId);
      setReviews(ReviewManager.getReviews(bookId));
    } catch (err) {
      setError(err.message);
      throw err;
    } finally {
      setIsLoading(false);
    }
  };

  // bookIdが変更された時にレビューを再読み込み
  useEffect(() => {
    if (bookId) {
      setReviews(ReviewManager.getReviews(bookId));
    }
  }, [bookId]);

  return {
    reviews,
    addReview,
    updateReview,
    deleteReview,
    isLoading,
    error
  };
}

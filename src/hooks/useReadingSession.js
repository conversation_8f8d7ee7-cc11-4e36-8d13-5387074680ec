import { useState, useEffect, useRef, useCallback } from 'react';
import { ReadingProgressManager } from '@/lib/storage.js';
import { getSpeechManager } from '@/lib/speechSynthesis.js';

/**
 * 読書セッション管理のカスタムフック
 * スクロール位置、読書時間、自動スクロールなどを管理
 */
export function useReadingSession(bookId, bookContent) {
  const [isReading, setIsReading] = useState(false);
  const [readingTime, setReadingTime] = useState(0);
  const [scrollPosition, setScrollPosition] = useState(0);
  const [autoScroll, setAutoScroll] = useState(false);
  const [autoScrollSpeed, setAutoScrollSpeed] = useState(50); // px/second
  
  const startTimeRef = useRef(null);
  const autoScrollIntervalRef = useRef(null);
  const lastScrollTimeRef = useRef(Date.now());
  const accumulatedTimeRef = useRef(0);

  // 読書セッション開始
  const startReading = useCallback(() => {
    if (!isReading) {
      setIsReading(true);
      startTimeRef.current = Date.now();
      lastScrollTimeRef.current = Date.now();
    }
  }, [isReading]);

  // 読書セッション終了
  const stopReading = useCallback(() => {
    if (isReading && startTimeRef.current) {
      const sessionTime = Date.now() - startTimeRef.current;
      accumulatedTimeRef.current += sessionTime;
      setReadingTime(prev => prev + sessionTime);
      setIsReading(false);
      startTimeRef.current = null;
    }
  }, [isReading]);

  // スクロール位置の保存
  const saveScrollPosition = useCallback((position) => {
    setScrollPosition(position);
    if (bookId && bookContent) {
      const percentage = Math.round((position / bookContent.length) * 100);
      ReadingProgressManager.saveProgress(bookId, {
        currentPosition: position,
        totalLength: bookContent.length,
        percentage: Math.min(percentage, 100),
        readingTime: Math.round((accumulatedTimeRef.current + readingTime) / 60000) // 分単位
      });
    }
  }, [bookId, bookContent, readingTime]);

  // 自動スクロール開始
  const startAutoScroll = useCallback(() => {
    if (autoScrollIntervalRef.current) {
      clearInterval(autoScrollIntervalRef.current);
    }
    
    setAutoScroll(true);
    autoScrollIntervalRef.current = setInterval(() => {
      window.scrollBy({
        top: autoScrollSpeed / 10, // 100ms間隔で実行されるため
        behavior: 'smooth'
      });
    }, 100);
  }, [autoScrollSpeed]);

  // 自動スクロール停止
  const stopAutoScroll = useCallback(() => {
    if (autoScrollIntervalRef.current) {
      clearInterval(autoScrollIntervalRef.current);
      autoScrollIntervalRef.current = null;
    }
    setAutoScroll(false);
  }, []);

  // 自動スクロール速度変更
  const changeAutoScrollSpeed = useCallback((speed) => {
    setAutoScrollSpeed(speed);
    if (autoScroll) {
      stopAutoScroll();
      setTimeout(() => startAutoScroll(), 100);
    }
  }, [autoScroll, startAutoScroll, stopAutoScroll]);

  // スクロール位置の復元
  const restoreScrollPosition = useCallback(() => {
    if (bookId) {
      const progress = ReadingProgressManager.getProgress(bookId);
      if (progress && progress.currentPosition > 0) {
        setTimeout(() => {
          window.scrollTo({
            top: progress.currentPosition,
            behavior: 'smooth'
          });
          setScrollPosition(progress.currentPosition);
        }, 500); // コンテンツの読み込み完了を待つ
      }
    }
  }, [bookId]);

  // 読書統計の取得
  const getReadingStats = useCallback(() => {
    const totalTime = accumulatedTimeRef.current + readingTime;
    const hours = Math.floor(totalTime / 3600000);
    const minutes = Math.floor((totalTime % 3600000) / 60000);
    const seconds = Math.floor((totalTime % 60000) / 1000);
    
    return {
      totalTime,
      hours,
      minutes,
      seconds,
      formattedTime: `${hours}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`,
      wordsPerMinute: bookContent ? Math.round((scrollPosition / bookContent.length) * bookContent.length / (totalTime / 60000)) : 0
    };
  }, [readingTime, scrollPosition, bookContent]);

  // スクロールイベントの監視
  useEffect(() => {
    const handleScroll = () => {
      const currentTime = Date.now();
      const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
      
      // スクロール位置を保存
      saveScrollPosition(scrollTop);
      
      // 読書セッションの自動開始/停止
      if (scrollTop > 0 && !isReading) {
        startReading();
      }
      
      // 一定時間スクロールがない場合は読書セッション停止
      lastScrollTimeRef.current = currentTime;
      setTimeout(() => {
        if (currentTime === lastScrollTimeRef.current && isReading) {
          stopReading();
        }
      }, 30000); // 30秒間操作がない場合
    };

    window.addEventListener('scroll', handleScroll, { passive: true });
    return () => window.removeEventListener('scroll', handleScroll);
  }, [isReading, startReading, stopReading, saveScrollPosition]);

  // 読書時間の更新
  useEffect(() => {
    let interval;
    if (isReading) {
      interval = setInterval(() => {
        setReadingTime(prev => prev + 1000);
      }, 1000);
    }
    return () => {
      if (interval) clearInterval(interval);
    };
  }, [isReading]);

  // クリーンアップ
  useEffect(() => {
    return () => {
      if (autoScrollIntervalRef.current) {
        clearInterval(autoScrollIntervalRef.current);
      }
      if (isReading) {
        stopReading();
      }
    };
  }, [isReading, stopReading]);

  // bookIdが変更された時の処理
  useEffect(() => {
    if (bookId) {
      // 前のセッションをリセット
      stopReading();
      setReadingTime(0);
      accumulatedTimeRef.current = 0;
      
      // 保存された進捗を復元
      const progress = ReadingProgressManager.getProgress(bookId);
      if (progress) {
        accumulatedTimeRef.current = (progress.readingTime || 0) * 60000; // 分から ms に変換
        setReadingTime(accumulatedTimeRef.current);
      }
    }
  }, [bookId, stopReading]);

  return {
    // 状態
    isReading,
    readingTime,
    scrollPosition,
    autoScroll,
    autoScrollSpeed,
    
    // アクション
    startReading,
    stopReading,
    saveScrollPosition,
    restoreScrollPosition,
    startAutoScroll,
    stopAutoScroll,
    changeAutoScrollSpeed,
    
    // ユーティリティ
    getReadingStats
  };
}

/**
 * 読書進捗の可視化フック
 */
export function useReadingProgress(bookContent) {
  const [progress, setProgress] = useState(0);
  const [estimatedTimeLeft, setEstimatedTimeLeft] = useState(0);

  useEffect(() => {
    const handleScroll = () => {
      if (!bookContent) return;

      const scrollTop = window.pageYOffset;
      const windowHeight = window.innerHeight;
      const documentHeight = document.documentElement.scrollHeight;
      
      const scrollableHeight = documentHeight - windowHeight;
      const scrollPercentage = scrollableHeight > 0 ? (scrollTop / scrollableHeight) * 100 : 0;
      
      setProgress(Math.min(Math.max(scrollPercentage, 0), 100));
      
      // 残り時間の推定（平均読書速度を300文字/分と仮定）
      const remainingContent = bookContent.length * (1 - scrollPercentage / 100);
      const estimatedMinutes = remainingContent / 300;
      setEstimatedTimeLeft(estimatedMinutes);
    };

    window.addEventListener('scroll', handleScroll, { passive: true });
    handleScroll(); // 初期値を設定
    
    return () => window.removeEventListener('scroll', handleScroll);
  }, [bookContent]);

  return {
    progress,
    estimatedTimeLeft: Math.round(estimatedTimeLeft)
  };
}

/**
 * キーボードショートカットフック
 */
export function useKeyboardShortcuts(actions) {
  useEffect(() => {
    const handleKeyDown = (event) => {
      // Ctrl/Cmd + キーの組み合わせのみ処理
      if (!event.ctrlKey && !event.metaKey) return;

      switch (event.key) {
        case 'ArrowUp':
          event.preventDefault();
          actions.scrollUp?.();
          break;
        case 'ArrowDown':
          event.preventDefault();
          actions.scrollDown?.();
          break;
        case ' ':
          event.preventDefault();
          actions.toggleAutoScroll?.();
          break;
        case 'b':
          event.preventDefault();
          actions.addBookmark?.();
          break;
        case 'f':
          event.preventDefault();
          actions.toggleFullscreen?.();
          break;
        case 'r':
          event.preventDefault();
          actions.toggleWritingMode?.();
          break;
        default:
          break;
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [actions]);
}

/**
 * テキスト選択とブックマーク機能のフック
 */
export function useTextSelection() {
  const [selectedText, setSelectedText] = useState('');
  const [selectionPosition, setSelectionPosition] = useState(0);
  const [showBookmarkButton, setShowBookmarkButton] = useState(false);
  const [buttonPosition, setButtonPosition] = useState({ x: 0, y: 0 });

  useEffect(() => {
    const handleSelectionChange = () => {
      const selection = window.getSelection();
      const text = selection.toString().trim();

      if (text.length > 0) {
        setSelectedText(text);

        // 選択範囲の位置を計算
        const range = selection.getRangeAt(0);
        const rect = range.getBoundingClientRect();

        // スクロール位置を考慮した絶対位置
        const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
        const position = scrollTop + rect.top;

        setSelectionPosition(position);
        setButtonPosition({
          x: rect.left + rect.width / 2,
          y: rect.bottom + window.pageYOffset
        });
        setShowBookmarkButton(true);
      } else {
        setSelectedText('');
        setShowBookmarkButton(false);
      }
    };

    const handleClickOutside = (event) => {
      // ブックマークボタン以外をクリックした場合は選択を解除
      if (!event.target.closest('.bookmark-button')) {
        setShowBookmarkButton(false);
      }
    };

    document.addEventListener('selectionchange', handleSelectionChange);
    document.addEventListener('click', handleClickOutside);

    return () => {
      document.removeEventListener('selectionchange', handleSelectionChange);
      document.removeEventListener('click', handleClickOutside);
    };
  }, []);

  const clearSelection = () => {
    window.getSelection().removeAllRanges();
    setSelectedText('');
    setShowBookmarkButton(false);
  };

  return {
    selectedText,
    selectionPosition,
    showBookmarkButton,
    buttonPosition,
    clearSelection
  };
}

/**
 * 音声読み上げ機能のフック
 */
export function useSpeechSynthesis(bookContent) {
  const [speechStatus, setSpeechStatus] = useState({
    isSupported: false,
    isPlaying: false,
    isPaused: false,
    currentPosition: 0,
    settings: {
      rate: 1.0,
      pitch: 1.0,
      volume: 1.0
    }
  });

  const speechManagerRef = useRef(null);

  useEffect(() => {
    speechManagerRef.current = getSpeechManager();

    const updateStatus = () => {
      const status = speechManagerRef.current.getStatus();
      setSpeechStatus(status);
    };

    // 初期状態を設定
    updateStatus();

    // コールバックを設定
    speechManagerRef.current.setCallbacks({
      onStart: updateStatus,
      onEnd: updateStatus,
      onPause: updateStatus,
      onResume: updateStatus,
      onError: updateStatus,
      onBoundary: updateStatus
    });

    return () => {
      // クリーンアップ
      speechManagerRef.current?.stop();
    };
  }, []);

  const startSpeech = useCallback(() => {
    if (speechManagerRef.current && bookContent) {
      speechManagerRef.current.speak(bookContent);
    }
  }, [bookContent]);

  const pauseSpeech = useCallback(() => {
    if (speechManagerRef.current) {
      speechManagerRef.current.pause();
    }
  }, []);

  const resumeSpeech = useCallback(() => {
    if (speechManagerRef.current) {
      speechManagerRef.current.resume();
    }
  }, []);

  const stopSpeech = useCallback(() => {
    if (speechManagerRef.current) {
      speechManagerRef.current.stop();
    }
  }, []);

  const updateSpeechSettings = useCallback((newSettings) => {
    if (speechManagerRef.current) {
      speechManagerRef.current.updateSettings(newSettings);

      // 設定を即座に反映
      if (newSettings.rate !== undefined) {
        speechManagerRef.current.setRate(newSettings.rate);
      }
      if (newSettings.pitch !== undefined) {
        speechManagerRef.current.setPitch(newSettings.pitch);
      }
      if (newSettings.volume !== undefined) {
        speechManagerRef.current.setVolume(newSettings.volume);
      }

      // 状態を更新
      setSpeechStatus(prev => ({
        ...prev,
        settings: { ...prev.settings, ...newSettings }
      }));
    }
  }, []);

  return {
    speechStatus,
    startSpeech,
    pauseSpeech,
    resumeSpeech,
    stopSpeech,
    updateSpeechSettings
  };
}

/**
 * 青空文庫のテキストデータを処理するユーティリティ
 */

// 青空文庫の注記記号を処理する関数
export function parseAozoraText(text) {
  if (!text) return '';
  
  let processedText = text;
  
  // ヘッダー情報を除去（作品本文の開始まで）
  const bodyStart = processedText.indexOf('-------------------------------------------------------');
  if (bodyStart !== -1) {
    const secondDivider = processedText.indexOf('-------------------------------------------------------', bodyStart + 1);
    if (secondDivider !== -1) {
      processedText = processedText.substring(secondDivider + 55);
    }
  }
  
  // フッター情報を除去（作品本文の終了から）
  const footerStart = processedText.lastIndexOf('底本：');
  if (footerStart !== -1) {
    processedText = processedText.substring(0, footerStart);
  }
  
  // 青空文庫の注記を処理
  processedText = processedText
    // ルビの処理 ｜漢字《かんじ》 → <ruby>漢字<rt>かんじ</rt></ruby>
    .replace(/｜([^《]+)《([^》]+)》/g, '<ruby>$1<rt>$2</rt></ruby>')
    // 簡単なルビの処理 漢字《かんじ》 → <ruby>漢字<rt>かんじ</rt></ruby>
    .replace(/([一-龯]+)《([^》]+)》/g, '<ruby>$1<rt>$2</rt></ruby>')
    // 傍点の処理 ［＃「文字」に傍点］
    .replace(/［＃「([^」]+)」に傍点］/g, '<em class="emphasis-dots">$1</em>')
    // 太字の処理 ［＃「文字」は太字］
    .replace(/［＃「([^」]+)」は太字］/g, '<strong>$1</strong>')
    // 改ページ ［＃改ページ］
    .replace(/［＃改ページ］/g, '<div class="page-break"></div>')
    // 字下げ ［＃ここから○字下げ］...［＃ここで字下げ終わり］
    .replace(/［＃ここから(\d+)字下げ］([\s\S]*?)［＃ここで字下げ終わり］/g, 
      '<div class="indent-$1">$2</div>')
    // 見出し ［＃「文字」は大見出し］
    .replace(/［＃「([^」]+)」は大見出し］/g, '<h1 class="chapter-title">$1</h1>')
    .replace(/［＃「([^」]+)」は中見出し］/g, '<h2 class="section-title">$1</h2>')
    .replace(/［＃「([^」]+)」は小見出し］/g, '<h3 class="subsection-title">$1</h3>')
    // その他の注記を除去
    .replace(/［＃[^］]*］/g, '')
    // 連続する空行を整理
    .replace(/\n\s*\n\s*\n/g, '\n\n')
    // 行頭の全角スペースを段落インデントに変換
    .replace(/^　+/gm, (match) => `<span class="indent-${match.length}"></span>`)
    // 改行をHTMLの改行に変換
    .replace(/\n/g, '<br>');
  
  return processedText.trim();
}

// 青空文庫のテキストファイルを取得する関数
export async function fetchAozoraText(url) {
  try {
    // CORSの問題を回避するため、プロキシサービスを使用
    const proxyUrl = `https://api.allorigins.win/get?url=${encodeURIComponent(url)}`;
    const response = await fetch(proxyUrl);
    
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }
    
    const data = await response.json();
    
    // Shift_JISからUTF-8への変換が必要な場合があるが、
    // ブラウザ環境では制限があるため、UTF-8版のURLを使用することを推奨
    return data.contents;
  } catch (error) {
    console.error('青空文庫テキストの取得に失敗しました:', error);
    throw error;
  }
}

// 青空文庫の作品情報を取得する関数（サンプル実装）
export async function fetchAozoraBookInfo(bookId) {
  // 実際の実装では青空文庫のAPIまたはスクレイピングを使用
  // ここではサンプルデータを返す
  const sampleBooks = {
    '148_789': {
      id: '148_789',
      title: '吾輩は猫である',
      author: '夏目漱石',
      description: '吾輩は猫である。名前はまだ無い。',
      category: '小説',
      textUrl: 'https://www.aozora.gr.jp/cards/000148/files/789_14547.html',
      publishedYear: '1905',
      genre: '小説'
    },
    '879_127': {
      id: '879_127',
      title: '羅生門',
      author: '芥川龍之介',
      description: 'ある日の暮方の事である。一人の下人が羅生門の下で雨やみを待っていた。',
      category: '短編小説',
      textUrl: 'https://www.aozora.gr.jp/cards/000879/files/127_15260.html',
      publishedYear: '1915',
      genre: '短編小説'
    }
  };
  
  return sampleBooks[bookId] || null;
}

// 青空文庫の作品一覧を取得する関数（拡張されたサンプルデータ）
export function getAozoraBookList() {
  return [
    {
      id: '148_789',
      title: '吾輩は猫である',
      author: '夏目漱石',
      description: '吾輩は猫である。名前はまだ無い。どこで生れたかとんと見当がつかぬ。',
      category: '小説',
      textUrl: 'https://www.aozora.gr.jp/cards/000148/files/789_14547.html',
      publishedYear: '1905',
      genre: '小説',
      length: '長編'
    },
    {
      id: '879_127',
      title: '羅生門',
      author: '芥川龍之介',
      description: 'ある日の暮方の事である。一人の下人が羅生門の下で雨やみを待っていた。',
      category: '短編小説',
      textUrl: 'https://www.aozora.gr.jp/cards/000879/files/127_15260.html',
      publishedYear: '1915',
      genre: '短編小説',
      length: '短編'
    },
    {
      id: '35_301',
      title: '人間失格',
      author: '太宰治',
      description: '恥の多い生涯を送って来ました。自分には、人間の生活というものが、見当つかないのです。',
      category: '小説',
      textUrl: 'https://www.aozora.gr.jp/cards/000035/files/301_14912.html',
      publishedYear: '1948',
      genre: '小説',
      length: '中編'
    },
    {
      id: '148_752',
      title: '坊っちゃん',
      author: '夏目漱石',
      description: '親譲りの無鉄砲で小供の時から損ばかりしている。',
      category: '小説',
      textUrl: 'https://www.aozora.gr.jp/cards/000148/files/752_14964.html',
      publishedYear: '1906',
      genre: '小説',
      length: '中編'
    },
    {
      id: '879_92',
      title: '蜘蛛の糸',
      author: '芥川龍之介',
      description: 'ある日の事でございます。お釈迦様は極楽の蓮池のふちを、独りでぶらぶらお歩きになっていらっしゃいました。',
      category: '短編小説',
      textUrl: 'https://www.aozora.gr.jp/cards/000879/files/92_14545.html',
      publishedYear: '1918',
      genre: '短編小説',
      length: '短編'
    },
    {
      id: '148_755',
      title: 'こころ',
      author: '夏目漱石',
      description: '私はその人を常に先生と呼んでいた。だからここでもただ先生と書くだけで本名は打ち明けない。',
      category: '小説',
      textUrl: 'https://www.aozora.gr.jp/cards/000148/files/773_14560.html',
      publishedYear: '1914',
      genre: '小説',
      length: '長編'
    },
    {
      id: '35_1565',
      title: '走れメロス',
      author: '太宰治',
      description: 'メロスは激怒した。必ず、かの邪智暴虐の王を除かなければならぬと決意した。',
      category: '短編小説',
      textUrl: 'https://www.aozora.gr.jp/cards/000035/files/1567_14913.html',
      publishedYear: '1940',
      genre: '短編小説',
      length: '短編'
    },
    {
      id: '879_128',
      title: '鼻',
      author: '芥川龍之介',
      description: '禅智内供の鼻と云えば、池の尾で知らない者はない。',
      category: '短編小説',
      textUrl: 'https://www.aozora.gr.jp/cards/000879/files/128_15261.html',
      publishedYear: '1916',
      genre: '短編小説',
      length: '短編'
    }
  ];
}

@import "tailwindcss";
@import "tw-animate-css";

@custom-variant dark (&:is(.dark *));

@theme inline {
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);
  --color-popover: var(--popover);
  --color-popover-foreground: var(--popover-foreground);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-destructive: var(--destructive);
  --color-border: var(--border);
  --color-input: var(--input);
  --color-ring: var(--ring);
  --color-chart-1: var(--chart-1);
  --color-chart-2: var(--chart-2);
  --color-chart-3: var(--chart-3);
  --color-chart-4: var(--chart-4);
  --color-chart-5: var(--chart-5);
  --color-sidebar: var(--sidebar);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-ring: var(--sidebar-ring);
}

:root {
  --radius: 0.625rem;
  --background: oklch(1 0 0);
  --foreground: oklch(0.145 0 0);
  --card: oklch(1 0 0);
  --card-foreground: oklch(0.145 0 0);
  --popover: oklch(1 0 0);
  --popover-foreground: oklch(0.145 0 0);
  --primary: oklch(0.205 0 0);
  --primary-foreground: oklch(0.985 0 0);
  --secondary: oklch(0.97 0 0);
  --secondary-foreground: oklch(0.205 0 0);
  --muted: oklch(0.97 0 0);
  --muted-foreground: oklch(0.556 0 0);
  --accent: oklch(0.97 0 0);
  --accent-foreground: oklch(0.205 0 0);
  --destructive: oklch(0.577 0.245 27.325);
  --border: oklch(0.922 0 0);
  --input: oklch(0.922 0 0);
  --ring: oklch(0.708 0 0);
  --chart-1: oklch(0.646 0.222 41.116);
  --chart-2: oklch(0.6 0.118 184.704);
  --chart-3: oklch(0.398 0.07 227.392);
  --chart-4: oklch(0.828 0.189 84.429);
  --chart-5: oklch(0.769 0.188 70.08);
  --sidebar: oklch(0.985 0 0);
  --sidebar-foreground: oklch(0.145 0 0);
  --sidebar-primary: oklch(0.205 0 0);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.97 0 0);
  --sidebar-accent-foreground: oklch(0.205 0 0);
  --sidebar-border: oklch(0.922 0 0);
  --sidebar-ring: oklch(0.708 0 0);
}

.dark {
  --background: oklch(0.145 0 0);
  --foreground: oklch(0.985 0 0);
  --card: oklch(0.205 0 0);
  --card-foreground: oklch(0.985 0 0);
  --popover: oklch(0.205 0 0);
  --popover-foreground: oklch(0.985 0 0);
  --primary: oklch(0.922 0 0);
  --primary-foreground: oklch(0.205 0 0);
  --secondary: oklch(0.269 0 0);
  --secondary-foreground: oklch(0.985 0 0);
  --muted: oklch(0.269 0 0);
  --muted-foreground: oklch(0.708 0 0);
  --accent: oklch(0.269 0 0);
  --accent-foreground: oklch(0.985 0 0);
  --destructive: oklch(0.704 0.191 22.216);
  --border: oklch(1 0 0 / 10%);
  --input: oklch(1 0 0 / 15%);
  --ring: oklch(0.556 0 0);
  --chart-1: oklch(0.488 0.243 264.376);
  --chart-2: oklch(0.696 0.17 162.48);
  --chart-3: oklch(0.769 0.188 70.08);
  --chart-4: oklch(0.627 0.265 303.9);
  --chart-5: oklch(0.645 0.246 16.439);
  --sidebar: oklch(0.205 0 0);
  --sidebar-foreground: oklch(0.985 0 0);
  --sidebar-primary: oklch(0.488 0.243 264.376);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.269 0 0);
  --sidebar-accent-foreground: oklch(0.985 0 0);
  --sidebar-border: oklch(1 0 0 / 10%);
  --sidebar-ring: oklch(0.556 0 0);
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
  }
}

/* 縦書き表示のスタイル */
.vertical-writing {
  writing-mode: vertical-rl;
  text-orientation: upright;
  direction: rtl;
}

.horizontal-writing {
  writing-mode: horizontal-tb;
  text-orientation: mixed;
  direction: ltr;
}

/* 読書画面のスタイル */
.reading-content {
  line-height: var(--line-height, 1.8);
  font-size: var(--font-size, 16px);
  font-family: var(--font-family, serif);
  max-width: var(--page-width, 800px);
  margin: 0 auto;
  padding: 2rem;
}

.reading-content.vertical {
  writing-mode: vertical-rl;
  text-orientation: upright;
  height: 80vh;
  overflow-x: auto;
  overflow-y: hidden;
  max-width: none;
  width: 100%;
  padding: 2rem 1rem;
  columns: auto;
  column-width: 400px;
  column-gap: 3rem;
  column-fill: auto;
}

.reading-content.horizontal {
  writing-mode: horizontal-tb;
  text-orientation: mixed;
  overflow-y: auto;
  overflow-x: hidden;
}

/* ルビのスタイル */
ruby {
  ruby-align: center;
}

ruby rt {
  font-size: 0.6em;
  color: var(--color-muted-foreground);
}

/* 傍点のスタイル */
.emphasis-dots {
  text-emphasis: filled circle;
  text-emphasis-position: over right;
  font-style: normal;
}

/* 字下げのスタイル */
.indent-1 { text-indent: 1em; }
.indent-2 { text-indent: 2em; }
.indent-3 { text-indent: 3em; }
.indent-4 { text-indent: 4em; }

/* 見出しのスタイル */
.chapter-title {
  font-size: 1.5em;
  font-weight: bold;
  margin: 2em 0 1em 0;
  text-align: center;
}

.section-title {
  font-size: 1.3em;
  font-weight: bold;
  margin: 1.5em 0 0.5em 0;
}

.subsection-title {
  font-size: 1.1em;
  font-weight: bold;
  margin: 1em 0 0.5em 0;
}

/* 改ページのスタイル */
.page-break {
  page-break-before: always;
  break-before: page;
  margin: 2em 0;
}

/* フォントサイズのバリエーション */
.font-small { font-size: 14px; }
.font-medium { font-size: 16px; }
.font-large { font-size: 18px; }
.font-xlarge { font-size: 20px; }

/* 行間のバリエーション */
.line-height-tight { line-height: 1.4; }
.line-height-normal { line-height: 1.6; }
.line-height-relaxed { line-height: 1.8; }
.line-height-loose { line-height: 2.0; }

/* ページ幅のバリエーション */
.page-width-narrow { max-width: 600px; }
.page-width-normal { max-width: 800px; }
.page-width-wide { max-width: 1000px; }

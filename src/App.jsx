import { useState, useEffect } from 'react'
import { <PERSON><PERSON> } from '@/components/ui/button.jsx'
import { Input } from '@/components/ui/input.jsx'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card.jsx'
import { Badge } from '@/components/ui/badge.jsx'
import { Slider } from '@/components/ui/slider.jsx'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select.jsx'
import { Switch } from '@/components/ui/switch.jsx'
import { Progress } from '@/components/ui/progress.jsx'
import { Search, Book, ArrowLeft, Settings, Moon, Sun, Heart, BookOpen, RotateCcw, Type, AlignLeft, AlignCenter, MessageSquare, Share, Bookmark, X } from 'lucide-react'
import { getAozoraBookList, fetchAozoraText, parseAozoraText, fetchAozoraTextFromHTML } from '@/lib/aozora.js'
import { useSettings, useReadingProgress, useFavorites, useReadingHistory, useReviews } from '@/hooks/useSettings.js'
import { useReadingSession, useReadingProgress as useReadingProgressHook, useKeyboardShortcuts, useTextSelection, useSpeechSynthesis } from '@/hooks/useReadingSession.js'
import { ReviewDialog, ReviewList } from '@/components/ReviewDialog.jsx'
import { ShareDialog, ShareBook } from '@/components/ShareDialog.jsx'
import { ReadingControls, ReadingProgressIndicator, ReadingStats } from '@/components/ReadingControls.jsx'
import { BookmarkDialog, BookmarkPanel } from '@/components/BookmarkManager.jsx'
import { AdvancedSearchDialog, SearchResultsInfo } from '@/components/AdvancedSearch.jsx'
import { registerServiceWorker, PWAInstallPrompt, OfflineManager, checkPWASupport } from '@/lib/pwa.js'
import './App.css'

function App() {
  const [currentView, setCurrentView] = useState('home') // 'home', 'search', 'reader', 'settings'
  const [searchQuery, setSearchQuery] = useState('')
  const [selectedBook, setSelectedBook] = useState(null)
  const [books, setBooks] = useState([])
  const [loading, setLoading] = useState(false)
  const [bookContent, setBookContent] = useState('')
  const [contentLoading, setContentLoading] = useState(false)
  const [pwaInstallPrompt, setPwaInstallPrompt] = useState(null)
  const [isOffline, setIsOffline] = useState(!navigator.onLine)
  const [isFullscreen, setIsFullscreen] = useState(false)
  const [showReadingStats, setShowReadingStats] = useState(false)
  const [filteredBooks, setFilteredBooks] = useState([])
  const [searchInfo, setSearchInfo] = useState(null)

  // カスタムフックを使用
  const { settings, updateSetting } = useSettings()
  const { progress, updateProgress } = useReadingProgress(selectedBook?.id)
  const { favorites, toggleFavorite, isFavorite } = useFavorites()
  const { addToHistory } = useReadingHistory()
  const { reviews, addReview, updateReview, deleteReview } = useReviews(selectedBook?.id)

  // 読書セッション管理
  const readingSession = useReadingSession(selectedBook?.id, bookContent)
  const readingProgressHook = useReadingProgressHook(bookContent)
  const textSelection = useTextSelection()
  const speechSynthesis = useSpeechSynthesis(bookContent)

  // スクロール関数
  const scrollUp = () => {
    window.scrollBy({ top: -window.innerHeight * 0.8, behavior: 'smooth' })
  }

  const scrollDown = () => {
    window.scrollBy({ top: window.innerHeight * 0.8, behavior: 'smooth' })
  }

  // フルスクリーン機能
  const toggleFullscreen = () => {
    if (!document.fullscreenElement) {
      document.documentElement.requestFullscreen().then(() => {
        setIsFullscreen(true)
      }).catch(console.error)
    } else {
      document.exitFullscreen().then(() => {
        setIsFullscreen(false)
      }).catch(console.error)
    }
  }

  // ブックマーク関連の関数
  const addBookmark = () => {
    if (selectedBook?.id) {
      const position = textSelection.selectionPosition || window.pageYOffset
      // ブックマークダイアログを開く処理は後で実装
      console.log('Add bookmark at position:', position)
    }
  }

  const jumpToBookmark = (bookmark) => {
    window.scrollTo({
      top: bookmark.position,
      behavior: 'smooth'
    })
  }

  // キーボードショートカット
  useKeyboardShortcuts({
    scrollUp,
    scrollDown,
    toggleAutoScroll: readingSession.autoScroll ? readingSession.stopAutoScroll : readingSession.startAutoScroll,
    addBookmark,
    toggleFullscreen,
    toggleWritingMode: () => updateSetting('writingMode', settings.writingMode === 'horizontal' ? 'vertical' : 'horizontal')
  })

  // 青空文庫のデータを取得
  useEffect(() => {
    const bookList = getAozoraBookList()
    setBooks(bookList)
    setFilteredBooks(bookList)
  }, [])

  // PWA機能の初期化
  useEffect(() => {
    // Service Worker の登録
    registerServiceWorker().catch(console.error)

    // PWA サポート状況をチェック
    const support = checkPWASupport()
    console.log('PWA Support:', support)

    // インストールプロンプトの設定
    const installPrompt = new PWAInstallPrompt()
    installPrompt.onInstallable = () => {
      setPwaInstallPrompt(installPrompt)
    }
    installPrompt.onInstalled = () => {
      setPwaInstallPrompt(null)
    }

    // オフライン状態の管理
    const offlineManager = new OfflineManager()
    offlineManager.onOnline(() => setIsOffline(false))
    offlineManager.onOffline(() => setIsOffline(true))

    // フルスクリーン状態の監視
    const handleFullscreenChange = () => {
      setIsFullscreen(!!document.fullscreenElement)
    }

    document.addEventListener('fullscreenchange', handleFullscreenChange)

    return () => {
      document.removeEventListener('fullscreenchange', handleFullscreenChange)
    }
  }, [])

  // 本の内容を取得する関数
  const loadBookContent = async (book) => {
    setContentLoading(true)
    try {
      // 実際の青空文庫テキストを取得
      let rawText;

      try {
        // まず青空文庫のHTMLから本文を取得を試行
        rawText = await fetchAozoraTextFromHTML(book.id);
      } catch (htmlError) {
        console.warn('HTMLからの取得に失敗、フォールバックテキストを使用:', htmlError);

        // フォールバック: 作品ごとの完全なサンプルテキスト
        const fallbackTexts = {
          '148_752': `坊っちゃん

夏目漱石

一

　親譲りの無鉄砲で小供の時から損ばかりしている。小学校に居る時分学校の二階から飛び降りて一週間ほど腰を抜かした事がある。なぜそんな無闇をしたと聞く人があるかも知れぬ。別段深い理由でもない。新築の二階から首を出していたら、同級生の一人が冗談に、いくら威張っても、そこから飛び降りる事は出来まい。弱虫やーい。と囃したからである。小使に負ぶさって帰って来た時、おやじが大きな眼をして二階ぐらいから飛び降りて腰を抜かす奴があるかと云ったから、この次は抜かさずに飛んで見せますと答えた。

　親類のものから西洋菓子をもらって来た時におやじが小僧の時分から甘い物が好きで困ると母に話しているのを聞いた事がある。それでも無闇に食いたがる。今度は大きな奴を一つ買ってやるからそれを食ったら、もうやめろと云い聞かせて、買ってくれた。ところが、そんな約束は小僧の時から守った事がない。一つ食ったらもう一つ食いたくなった。母が、もうおよしなさいと云ったが、どうしても食う。とうとう泣き出した。母が泣くのを見て、おやじはこの馬鹿者とどやし付けた。

　こんな性分だから、普通の人が大抵経験する通りの失敗は大抵やった。しかし、決して卑怯な事はしなかった。小学校の時分、弱虫やーいと囃された時も、悔しかったけれども泣かなかった。ただ飛び降りて見せた。中学の時分、野だを相手にけんかをした時も、野だが卑怯な真似をしても、おれは正々堂々と戦った。

　こんな風だから、損ばかりしている。おやじが死んでからは、おふくろと兄と三人で暮していた。おやじは何をしていたか知らない。ろくな身分でなかった事は慥である。おふくろは病気で、いつも心配していた。兄は商業学校を卒業してから何かやっていたが、これも碌でもない事をやっていたらしい。

　おれは中学を卒業すると同時に、物理学校へ入学した。むずかしい事は嫌いだったが、別に勉強をしたと云う記憶もない。ただ卒業してしまった。それから学校の世話で、ある私立中学校の数学の教師になった。月給は四十円だった。`,

          '879_127': `羅生門

芥川龍之介

　ある日の暮方の事である。一人の下人が羅生門の下で雨やみを待っていた。

　広い門の下には、この男のほかに誰もいない。ただ、所々丹塗の剥げた、大きな円柱に、蟋蟀が一匹とまっている。羅生門が、朱雀大路にある以上は、この男のほかにも、雨やみを待っている人があってもよさそうなものである。それが、この男のほかには誰もいない。

　何故かと云うと、この二三年、京都には、地震とか辻風とか火事とか饑饉とかいう災いがつづいて起った。そこで洛中のさびれ方は一通りでない。旧記によると、仏像や仏具を打砕いて、その丹がついたり、金銀の箔がついたりした木を、路ばたに積んで薪の料に売っていたという事である。洛中がその始末であるから、羅生門の修理などは、元より誰も捨てて顧る者がなかった。するとその荒れ果てたのをよい事にして、狐狸が棲む。盗人が棲む。とうとうしまいには、引取り手のない死人を、この門へ持って来て、棄てて行くという習慣さえ出来た。そこで、日の目が見えなくなると、誰でも気味を悪がって、この門の近所へは足ぶみをしない事になってしまった。

　その代りまた鴉がたくさん集って来た。昼間見ると、その鴉が何羽となく輪を描いて、高い鴟尾のまわりを啼きながら飛んでいる。ことに門の上の空が、夕焼けであかくなる時には、それが胡麻をまいたようにはっきり見えた。鴉は、勿論、門の上にある死人の肉を、啄みに来るのである。――もっとも今日は、刻限が遅いせいか、一羽も見えない。

　その鴉がいないのも、この男には別に気にかからなかった。この男は、さっきから朱雀大路にいる多くの死人の事を考えていた。そうして、自分もその死人の仲間に入るのではなかろうかと思った。――そう思うと、急に、この門の下で雨やみを待っていると云う事が、ひどく心細い事に思われて来た。雨は、羅生門をつつんで、遠くから、ざあっと云う音をたてて来る。夕闇は次第に空を低くして、見上げると、門の屋根が、重たい雲の底に支えられている。

　どうにもならない事を、どうにかするためには、手段を選んでいる遑はない。選んでいれば、築土の下か、道ばたの土の上で、饑死をするばかりである。そうして、この門の上へ持って来て、犬のように棄てられてしまうばかりである。選ばないとすれば――下人の考えは、何度も同じ道を低徊した後で、やっとこの局所へ逢着した。しかしこの「すれば」は、いつまでたっても、結局「すれば」であった。下人は、手段を選ばないという事を肯定しながらも、この「すれば」のかたをつけるために、当面の勇気が出ずにいた。`,

          '35_1565': `走れメロス

太宰治

　メロスは激怒した。必ず、かの邪智暴虐の王を除かなければならぬと決意した。メロスには政治がわからぬ。メロスは、村の牧人である。笛を吹き、羊と遊んで暮して来た。けれども邪悪に対しては、人一倍に敏感であった。きょう未明メロスは村を出発し、野を越え山越え、十里はなれた此のシラクスの市にやって来た。妹の結婚式も間近かなので、花嫁の衣裳やら祝宴の御馳走やらを買いに来たのである。先ず、その品々を買い集め、それから都の大路をぶらぶら歩いた。メロスには竹馬の友があった。セリヌンティウスである。今は此のシラクスの市で、石工をしている。その友を、これから訪ねてみるつもりなのである。久しく逢わなかったのだから、訪ねて行くのが楽しみである。歩いているうちにメロスは、まちの様子を怪しく思った。ひっそりしている。もう既に日も落ちて、まちの暗いのは当りまえだが、けれども、なんだか、夜のせいばかりでは無い、市全体が、やけに寂しい。のんきなメロスも、だんだん不安になって来た。路で逢った若い衆をつかまえて、何かあったのか、二年まえに此の市に来たときは、夜でも皆が歌をうたって、まちは賑やかであった筈だが、と質問した。若い衆は、首を振って答えなかった。しばらく歩いて老爺に逢い、こんどはもっと、語勢を強くして質問した。老爺は答えなかった。メロスは両手で老爺の肩を強くゆすぶって質問を重ねた。老爺は、あたりをはばかる低声で、わずか答えた。

「王様は、人を殺します。」

「なぜ殺すのだ。」

「悪心を抱いている、というのが王様のお考えです。」

「たれが、どんな悪心を抱いているというのだ。」

「それは、わからぬ。王様は、人の心を疑っていらっしゃる。そうして、少しく疑わしいと思えば、すぐに殺してしまうのです。こんな世の中では、だれも安心して暮していられません。」

「そうか。」メロスは、単純な男であったから、王の心を推し量ってみる事が出来なかった。「それは、ひどい王様だ。生かして置けぬ。」

　メロスは、単純な男であったから、人を疑うという事を知らなかった。メロスは、人を信ずる事を知っていた。メロスは、王の前に出た。

「陛下。」メロスは、きっぱりと言った。「人を、信ずる事が出来ぬ者に、統治の資格はございません。」

　暴君ディオニスは、ひとり合点して、くすくす笑った。

「おまえがそれほど、人を信ずるならば、ちょっとおもしろい見ものをお目にかけよう。あの男を磔にかけよ。」

　指さされた男は、メロスの友人であった。`,

          '879_92': `蜘蛛の糸

芥川龍之介

　ある日の事でございます。お釈迦様は極楽の蓮池のふちを、独りでぶらぶらお歩きになっていらっしゃいました。池の中に咲いている蓮の花は、みんな玉のようにまっ白で、そのまん中にある金色の蕊からは、なんとも云えない好い匂いが、絶間なくあたりへ溢れて居ります。極楽は丁度朝なのでございましょう。

　やがてお釈迦様はその池のふちに御足をお止めになって、水の面を蔽っている蓮の葉の間から、ふと下の容子を御覧になりました。この極楽の蓮池の下は、丁度地獄の底に当って居りますから、水晶のような水を透き徹して、三途の河や針の山の景色が、丁度覗き眼鏡を見るように、はっきりと見えるのでございます。

　すると、その地獄の底に、犍陀多という男が一人、ほかの罪人と一しょに蠢いている姿が、お眼に止まりました。この犍陀多と云う男は、人を殺したり家に火をつけたりした大泥坊でございますが、それでもたった一つ、善い事を致した覚えがございます。と申しますのは、ある時深い林の中を通りますと、小さな蜘蛛が一匹、路ばたを這って行くのを見つけました。そこで犍陀多は早速足を上げて、踏み殺そうと致しましたが、「いや、いや、これも小さいながら、命のあるものに違いない。その命を無暗にとると云う事は、いくら何でも可哀そうだ。」と、こう急に思い返して、とうとうその蜘蛛を殺さずに助けてやったからでございます。

　お釈迦様は、犍陀多のこの善い事を思い出しになって、もしできる事なら、この男を地獄から救い出してやろうと御考えになりました。幸い、側を見ますと、翡翠のような色をした蓮の葉の上に、極楽の蜘蛛が一匹、美しい銀色の糸をかけて居ります。お釈迦様はその蜘蛛の糸をそっと御手にお取りになって、玉のような白い蓮の花の間から、遙か下にある地獄の底へ、まっすぐにお下しになりました。

　こちらは地獄の底の血の池で、ほかの罪人と一しょに、浮いたり沈んだりしていた犍陀多でございます。何しろ周囲は真っ暗で、たまにその暗やみから浮き上がって来るものと云えば、恐ろしい針の山の針が光るばかりでございます。その上あたりは墓の中のようにしんと静まり返って、たまに聞えるものと云えば、罪人がつく微かな嘆息ばかりでございます。これらは皆この地獄へ落ちて来るほどの悪人どもでございますから、もう疲れ果てて、泣声を出す力さえなくなっているのでございましょう。ですからさすがの犍陀多も、血の池の血に咽びながら、まるで死にかかった蛙のように、ただもがいてばかり居りました。

　ところがある時の事でございます。何気なく犍陀多が頭を上げて、血の池の空を眺めますと、そのひっそりとした暗やみの中を、銀色の蜘蛛の糸が、まるで人目にかからぬように、するすると自分の上へ垂れて参るのではございませんか。犍陀多は、これを見ると、思わず手を拍って喜びました。この糸に縋りついて、どこまでものぼって行けば、きっと地獄からぬけ出せるのに相違ございません。いや、うまく行くと、極楽へはいる事さえも出来ましょう。そうすれば、もう針の山へ追い上げられる事もなければ、血の池に沈められる事もある筈はございません。`
        };

        rawText = fallbackTexts[book.id] || `${book.title}

${book.author}

${book.description}

申し訳ございません。現在、この作品の本文を読み込むことができません。
青空文庫のサーバーへの接続に問題が発生している可能性があります。

しばらく時間をおいてから再度お試しください。`;
      }

      // 青空文庫のテキストを処理してHTMLに変換
      const processedContent = parseAozoraText(rawText);
      setBookContent(processedContent);

      // 読書進捗を更新（テキストの長さを設定）
      if (settings.autoSave && rawText) {
        await updateProgress({
          totalLength: rawText.length,
          currentPosition: 0
        });
      }

    } catch (error) {
      console.error('本の内容の取得に失敗しました:', error);
      setBookContent(`<div class="error-message">
        <h3>エラー: 本文を読み込めませんでした</h3>
        <p>青空文庫のサーバーに接続できませんでした。</p>
        <p>しばらく時間をおいてから再度お試しください。</p>
        <p class="error-details">エラー詳細: ${error.message}</p>
      </div>`);
    } finally {
      setContentLoading(false);
    }
  }

  const handleSearch = (query) => {
    setSearchQuery(query)

    if (!query.trim()) {
      setFilteredBooks(books)
      setSearchInfo(null)
      return
    }

    const results = books.filter(book =>
      book.title.toLowerCase().includes(query.toLowerCase()) ||
      book.author.toLowerCase().includes(query.toLowerCase()) ||
      book.description.toLowerCase().includes(query.toLowerCase())
    )

    setFilteredBooks(results)
    setSearchInfo({ query, filters: { authors: [], categories: [], genres: [], yearRange: [1900, 2024], lengths: [] } })
  }

  const handleAdvancedSearch = (results, searchInfo) => {
    setFilteredBooks(results)
    setSearchInfo(searchInfo)
  }

  const clearSearch = () => {
    setSearchQuery('')
    setFilteredBooks(books)
    setSearchInfo(null)
  }

  const handleBookSelect = async (book) => {
    setSelectedBook(book)
    setCurrentView('reader')

    // 読書履歴に追加
    await addToHistory(book.id, book)

    // 本の内容を読み込み
    await loadBookContent(book)
  }

  const renderHome = () => (
    <div className="min-h-screen bg-background">
      <header className="border-b border-border bg-card">
        <div className="container mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <Book className="h-8 w-8 text-primary" />
              <h1 className="text-2xl font-bold text-foreground">青空文庫リーダー</h1>
            </div>
            <div className="flex items-center space-x-2">
              {isOffline && (
                <Badge variant="destructive" className="text-xs">
                  オフライン
                </Badge>
              )}
              {pwaInstallPrompt && (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => pwaInstallPrompt.showInstallPrompt()}
                >
                  アプリをインストール
                </Button>
              )}
              <Button
                variant="ghost"
                size="icon"
                onClick={() => updateSetting('darkMode', !settings.darkMode)}
              >
                {settings.darkMode ? <Sun className="h-5 w-5" /> : <Moon className="h-5 w-5" />}
              </Button>
              <Button
                variant="ghost"
                size="icon"
                onClick={() => setCurrentView('settings')}
              >
                <Settings className="h-5 w-5" />
              </Button>
            </div>
          </div>
        </div>
      </header>

      <main className="container mx-auto px-4 py-8">
        <div className="mb-8 space-y-4">
          <div className="flex items-center space-x-4">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
              <Input
                placeholder="作品名や著者名で検索..."
                value={searchQuery}
                onChange={(e) => handleSearch(e.target.value)}
                className="pl-10"
              />
            </div>
            <AdvancedSearchDialog
              books={books}
              onSearchResults={handleAdvancedSearch}
            />
            {searchInfo && (
              <Button variant="outline" onClick={clearSearch}>
                <X className="h-4 w-4 mr-2" />
                クリア
              </Button>
            )}
          </div>

          <SearchResultsInfo results={filteredBooks} searchInfo={searchInfo} />
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredBooks.map((book) => {
            const bookProgress = progress && progress.bookId === book.id ? progress : { percentage: 0 };
            const isBookFavorite = isFavorite(book.id);

            return (
              <Card key={book.id} className="cursor-pointer hover:shadow-lg transition-shadow">
                <CardHeader>
                  <div className="flex justify-between items-start">
                    <div className="flex-1">
                      <CardTitle className="text-lg">{book.title}</CardTitle>
                      <CardDescription className="text-sm text-muted-foreground">
                        {book.author} • {book.publishedYear}年
                      </CardDescription>
                      {bookProgress.percentage > 0 && (
                        <div className="mt-2">
                          <div className="flex items-center justify-between text-xs text-muted-foreground mb-1">
                            <span>読書進捗</span>
                            <span>{bookProgress.percentage}%</span>
                          </div>
                          <Progress value={bookProgress.percentage} className="h-1" />
                        </div>
                      )}
                    </div>
                    <div className="flex items-center space-x-2">
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={(e) => {
                          e.stopPropagation();
                          toggleFavorite(book.id);
                        }}
                      >
                        <Heart className={`h-4 w-4 ${isBookFavorite ? 'fill-red-500 text-red-500' : ''}`} />
                      </Button>
                      <Badge variant="secondary">{book.category}</Badge>
                    </div>
                  </div>
                </CardHeader>
                <CardContent>
                  <p className="text-sm text-muted-foreground mb-4 line-clamp-3">
                    {book.description}
                  </p>
                  <div className="flex items-center justify-between">
                    <div className="text-xs text-muted-foreground">
                      {book.length} • {book.genre}
                    </div>
                    <Button
                      onClick={() => handleBookSelect(book)}
                      size="sm"
                    >
                      <BookOpen className="h-4 w-4 mr-2" />
                      読む
                    </Button>
                  </div>
                </CardContent>
              </Card>
            );
          })}
        </div>

        {filteredBooks.length === 0 && searchQuery && (
          <div className="text-center py-12">
            <p className="text-muted-foreground">検索結果が見つかりませんでした。</p>
          </div>
        )}
      </main>
    </div>
  )

  const renderReader = () => (
    <div className="min-h-screen bg-background">
      <header className="border-b border-border bg-card sticky top-0 z-10">
        <div className="container mx-auto px-4 py-3">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <Button
                variant="ghost"
                size="icon"
                onClick={() => setCurrentView('home')}
              >
                <ArrowLeft className="h-5 w-5" />
              </Button>
              <div>
                <h1 className="text-lg font-bold text-foreground">{selectedBook?.title}</h1>
                <p className="text-sm text-muted-foreground">{selectedBook?.author}</p>
              </div>
            </div>
            <div className="flex items-center space-x-2">
              {progress && progress.percentage > 0 && (
                <div className="hidden md:flex items-center space-x-2 text-sm text-muted-foreground">
                  <span>{progress.percentage}%</span>
                  <Progress value={progress.percentage} className="w-20 h-2" />
                </div>
              )}
              <ShareBook
                book={selectedBook}
                progress={progress}
                review={reviews.length > 0 ? reviews[0] : null}
              />
              <Button
                variant="ghost"
                size="icon"
                onClick={() => toggleFavorite(selectedBook?.id)}
              >
                <Heart className={`h-5 w-5 ${isFavorite(selectedBook?.id) ? 'fill-red-500 text-red-500' : ''}`} />
              </Button>
              <Button
                variant="ghost"
                size="icon"
                onClick={() => updateSetting('writingMode', settings.writingMode === 'horizontal' ? 'vertical' : 'horizontal')}
              >
                {settings.writingMode === 'horizontal' ? <AlignLeft className="h-5 w-5" /> : <AlignCenter className="h-5 w-5" />}
              </Button>
              <BookmarkPanel
                bookId={selectedBook?.id}
                onJumpToBookmark={jumpToBookmark}
              />
              <Button
                variant="ghost"
                size="icon"
                onClick={() => setShowReadingStats(!showReadingStats)}
                title="読書統計を表示"
              >
                <BookOpen className="h-5 w-5" />
              </Button>
              <Button
                variant="ghost"
                size="icon"
                onClick={() => updateSetting('darkMode', !settings.darkMode)}
              >
                {settings.darkMode ? <Sun className="h-5 w-5" /> : <Moon className="h-5 w-5" />}
              </Button>
            </div>
          </div>
        </div>
      </header>

      {/* 読書進捗インジケーター */}
      <ReadingProgressIndicator
        progress={readingProgressHook.progress}
        isVisible={currentView === 'reader'}
      />

      <main className="container mx-auto px-4 py-6">
        {contentLoading ? (
          <div className="flex items-center justify-center py-12">
            <div className="text-center">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
              <p className="text-muted-foreground">本文を読み込み中...</p>
            </div>
          </div>
        ) : (
          <div className="space-y-8">
            <div
              className={`reading-content ${settings.writingMode === 'vertical' ? 'vertical' : 'horizontal'}`}
              dangerouslySetInnerHTML={{ __html: bookContent }}
              onLoad={() => {
                // コンテンツ読み込み完了後にスクロール位置を復元
                setTimeout(() => {
                  readingSession.restoreScrollPosition()
                }, 100)
              }}
            />

            {/* レビューセクション */}
            <div className="max-w-4xl mx-auto">
              <Card>
                <CardHeader>
                  <div className="flex items-center justify-between">
                    <div>
                      <CardTitle className="flex items-center space-x-2">
                        <MessageSquare className="h-5 w-5" />
                        <span>レビュー・感想</span>
                      </CardTitle>
                      <CardDescription>
                        この作品についての感想をお聞かせください
                      </CardDescription>
                    </div>
                    <ReviewDialog
                      bookId={selectedBook?.id}
                      bookTitle={selectedBook?.title}
                      onReviewSubmit={addReview}
                    />
                  </div>
                </CardHeader>
                <CardContent>
                  <ReviewList
                    reviews={reviews}
                    onEdit={updateReview}
                    onDelete={deleteReview}
                    canEdit={true}
                  />
                </CardContent>
              </Card>
            </div>
          </div>
        )}
      </main>

      {/* 読書コントロール */}
      {currentView === 'reader' && !contentLoading && (
        <>
          <ReadingControls
            isReading={readingSession.isReading}
            readingTime={readingSession.readingTime}
            progress={readingProgressHook.progress}
            estimatedTimeLeft={readingProgressHook.estimatedTimeLeft}
            autoScroll={readingSession.autoScroll}
            autoScrollSpeed={readingSession.autoScrollSpeed}
            onStartReading={readingSession.startReading}
            onStopReading={readingSession.stopReading}
            onStartAutoScroll={readingSession.startAutoScroll}
            onStopAutoScroll={readingSession.stopAutoScroll}
            onChangeAutoScrollSpeed={readingSession.changeAutoScrollSpeed}
            onScrollUp={scrollUp}
            onScrollDown={scrollDown}
            onToggleFullscreen={toggleFullscreen}
            isFullscreen={isFullscreen}
            speechStatus={speechSynthesis.speechStatus}
            onStartSpeech={speechSynthesis.startSpeech}
            onPauseSpeech={speechSynthesis.pauseSpeech}
            onResumeSpeech={speechSynthesis.resumeSpeech}
            onStopSpeech={speechSynthesis.stopSpeech}
            onSpeechSettingsChange={speechSynthesis.updateSpeechSettings}
          />

          <ReadingStats
            stats={readingSession.getReadingStats()}
            isVisible={showReadingStats}
          />

          {/* テキスト選択時のブックマークボタン */}
          {textSelection.showBookmarkButton && (
            <div
              className="fixed z-50 bookmark-button"
              style={{
                left: textSelection.buttonPosition.x - 50,
                top: textSelection.buttonPosition.y + 10
              }}
            >
              <BookmarkDialog
                bookId={selectedBook?.id}
                currentPosition={textSelection.selectionPosition}
                selectedText={textSelection.selectedText}
                onBookmarkAdded={() => textSelection.clearSelection()}
                trigger={
                  <Button size="sm" className="shadow-lg">
                    <Bookmark className="h-4 w-4 mr-2" />
                    ブックマーク
                  </Button>
                }
              />
            </div>
          )}
        </>
      )}
    </div>
  )

  const renderSettings = () => (
    <div className="min-h-screen bg-background">
      <header className="border-b border-border bg-card">
        <div className="container mx-auto px-4 py-4">
          <div className="flex items-center space-x-4">
            <Button
              variant="ghost"
              size="icon"
              onClick={() => setCurrentView('home')}
            >
              <ArrowLeft className="h-5 w-5" />
            </Button>
            <h1 className="text-xl font-bold text-foreground">設定</h1>
          </div>
        </div>
      </header>

      <main className="container mx-auto px-4 py-8 max-w-2xl space-y-6">
        {/* 表示設定 */}
        <Card>
          <CardHeader>
            <CardTitle>表示設定</CardTitle>
            <CardDescription>読書体験をカスタマイズできます</CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="flex items-center justify-between">
              <div>
                <h3 className="font-medium">ダークモード</h3>
                <p className="text-sm text-muted-foreground">暗い背景で表示します</p>
              </div>
              <Switch
                checked={settings.darkMode}
                onCheckedChange={(checked) => updateSetting('darkMode', checked)}
              />
            </div>

            <div className="space-y-3">
              <div>
                <h3 className="font-medium mb-2">表示方向</h3>
                <Select
                  value={settings.writingMode}
                  onValueChange={(value) => updateSetting('writingMode', value)}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="horizontal">横書き</SelectItem>
                    <SelectItem value="vertical">縦書き</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div className="space-y-3">
              <div>
                <h3 className="font-medium mb-2">フォントサイズ: {settings.fontSize}px</h3>
                <Slider
                  value={[settings.fontSize]}
                  onValueChange={([value]) => updateSetting('fontSize', value)}
                  min={12}
                  max={24}
                  step={1}
                  className="w-full"
                />
              </div>
            </div>

            <div className="space-y-3">
              <div>
                <h3 className="font-medium mb-2">行間: {settings.lineHeight}</h3>
                <Slider
                  value={[settings.lineHeight]}
                  onValueChange={([value]) => updateSetting('lineHeight', value)}
                  min={1.2}
                  max={2.5}
                  step={0.1}
                  className="w-full"
                />
              </div>
            </div>

            <div className="space-y-3">
              <div>
                <h3 className="font-medium mb-2">フォント</h3>
                <Select
                  value={settings.fontFamily}
                  onValueChange={(value) => updateSetting('fontFamily', value)}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="serif">明朝体</SelectItem>
                    <SelectItem value="sans-serif">ゴシック体</SelectItem>
                    <SelectItem value="monospace">等幅フォント</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div className="space-y-3">
              <div>
                <h3 className="font-medium mb-2">ページ幅</h3>
                <Select
                  value={settings.pageWidth}
                  onValueChange={(value) => updateSetting('pageWidth', value)}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="narrow">狭い</SelectItem>
                    <SelectItem value="normal">標準</SelectItem>
                    <SelectItem value="wide">広い</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* 読書設定 */}
        <Card>
          <CardHeader>
            <CardTitle>読書設定</CardTitle>
            <CardDescription>読書進捗や履歴の管理設定</CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="flex items-center justify-between">
              <div>
                <h3 className="font-medium">自動保存</h3>
                <p className="text-sm text-muted-foreground">読書進捗を自動的に保存します</p>
              </div>
              <Switch
                checked={settings.autoSave}
                onCheckedChange={(checked) => updateSetting('autoSave', checked)}
              />
            </div>

            <div className="space-y-3">
              <div>
                <h3 className="font-medium mb-2">読書速度: {settings.readingSpeed}文字/分</h3>
                <Slider
                  value={[settings.readingSpeed]}
                  onValueChange={([value]) => updateSetting('readingSpeed', value)}
                  min={100}
                  max={800}
                  step={50}
                  className="w-full"
                />
                <p className="text-xs text-muted-foreground mt-1">
                  読書時間の推定に使用されます
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* 実装済み機能 */}
        <Card>
          <CardHeader>
            <CardTitle>実装済み機能</CardTitle>
            <CardDescription>現在利用可能な機能一覧</CardDescription>
          </CardHeader>
          <CardContent>
            <ul className="text-sm space-y-2">
              <li className="flex items-center space-x-2">
                <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                <span>縦書き/横書き表示の切り替え</span>
              </li>
              <li className="flex items-center space-x-2">
                <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                <span>フォントサイズ・行間の調整</span>
              </li>
              <li className="flex items-center space-x-2">
                <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                <span>読書進捗の保存</span>
              </li>
              <li className="flex items-center space-x-2">
                <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                <span>お気に入り機能</span>
              </li>
              <li className="flex items-center space-x-2">
                <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                <span>読書履歴</span>
              </li>
              <li className="flex items-center space-x-2">
                <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                <span>設定の永続化</span>
              </li>
            </ul>
          </CardContent>
        </Card>
      </main>
    </div>
  )

  switch (currentView) {
    case 'reader':
      return renderReader()
    case 'settings':
      return renderSettings()
    default:
      return renderHome()
  }
}

export default App


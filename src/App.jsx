import { useState, useEffect } from 'react'
import { But<PERSON> } from '@/components/ui/button.jsx'
import { Input } from '@/components/ui/input.jsx'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card.jsx'
import { Badge } from '@/components/ui/badge.jsx'
import { Slider } from '@/components/ui/slider.jsx'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select.jsx'
import { Switch } from '@/components/ui/switch.jsx'
import { Progress } from '@/components/ui/progress.jsx'
import { Search, Book, ArrowLeft, Settings, Moon, Sun, Heart, BookOpen, RotateCcw, Type, AlignLeft, AlignCenter, MessageSquare, Share } from 'lucide-react'
import { getAozoraBookList, fetchAozoraText, parseAozoraText } from '@/lib/aozora.js'
import { useSettings, useReadingProgress, useFavorites, useReadingHistory, useReviews } from '@/hooks/useSettings.js'
import { ReviewDialog, ReviewList } from '@/components/ReviewDialog.jsx'
import { ShareDialog, ShareBook } from '@/components/ShareDialog.jsx'
import { registerServiceWorker, PWAInstallPrompt, OfflineManager, checkPWASupport } from '@/lib/pwa.js'
import './App.css'

function App() {
  const [currentView, setCurrentView] = useState('home') // 'home', 'search', 'reader', 'settings'
  const [searchQuery, setSearchQuery] = useState('')
  const [selectedBook, setSelectedBook] = useState(null)
  const [books, setBooks] = useState([])
  const [loading, setLoading] = useState(false)
  const [bookContent, setBookContent] = useState('')
  const [contentLoading, setContentLoading] = useState(false)
  const [pwaInstallPrompt, setPwaInstallPrompt] = useState(null)
  const [isOffline, setIsOffline] = useState(!navigator.onLine)

  // カスタムフックを使用
  const { settings, updateSetting } = useSettings()
  const { progress, updateProgress } = useReadingProgress(selectedBook?.id)
  const { favorites, toggleFavorite, isFavorite } = useFavorites()
  const { addToHistory } = useReadingHistory()
  const { reviews, addReview, updateReview, deleteReview } = useReviews(selectedBook?.id)

  // 青空文庫のデータを取得
  useEffect(() => {
    setBooks(getAozoraBookList())
  }, [])

  // PWA機能の初期化
  useEffect(() => {
    // Service Worker の登録
    registerServiceWorker().catch(console.error)

    // PWA サポート状況をチェック
    const support = checkPWASupport()
    console.log('PWA Support:', support)

    // インストールプロンプトの設定
    const installPrompt = new PWAInstallPrompt()
    installPrompt.onInstallable = () => {
      setPwaInstallPrompt(installPrompt)
    }
    installPrompt.onInstalled = () => {
      setPwaInstallPrompt(null)
    }

    // オフライン状態の管理
    const offlineManager = new OfflineManager()
    offlineManager.onOnline(() => setIsOffline(false))
    offlineManager.onOffline(() => setIsOffline(true))

    return () => {
      // クリーンアップは特に必要なし
    }
  }, [])

  // 本の内容を取得する関数
  const loadBookContent = async (book) => {
    setContentLoading(true)
    try {
      // デモ用のサンプルテキスト（実際の実装では fetchAozoraText を使用）
      const sampleContent = `${book.title}

${book.author}

${book.description}

これは青空文庫の作品のサンプル表示です。実際のアプリでは、青空文庫のテキストファイルを取得して、ルビや注記を含む完全な本文を表示します。

この作品は「${book.category}」に分類され、${book.publishedYear}年に発表されました。

青空文庫の注記記号の例：
- ルビ：｜漢字《かんじ》
- 傍点：［＃「文字」に傍点］
- 太字：［＃「文字」は太字］
- 見出し：［＃「章題」は大見出し］

実際の青空文庫テキストでは、これらの記号が適切にHTMLに変換されて表示されます。

縦書き表示や横書き表示の切り替え、フォントサイズの調整、読書進捗の保存などの機能も利用できます。`;

      const processedContent = parseAozoraText(sampleContent)
      setBookContent(processedContent)
    } catch (error) {
      console.error('本の内容の取得に失敗しました:', error)
      setBookContent('本の内容を読み込めませんでした。')
    } finally {
      setContentLoading(false)
    }
  }

  const filteredBooks = books.filter(book =>
    book.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
    book.author.toLowerCase().includes(searchQuery.toLowerCase())
  )

  const handleBookSelect = async (book) => {
    setSelectedBook(book)
    setCurrentView('reader')

    // 読書履歴に追加
    await addToHistory(book.id, book)

    // 本の内容を読み込み
    await loadBookContent(book)
  }

  const renderHome = () => (
    <div className="min-h-screen bg-background">
      <header className="border-b border-border bg-card">
        <div className="container mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <Book className="h-8 w-8 text-primary" />
              <h1 className="text-2xl font-bold text-foreground">青空文庫リーダー</h1>
            </div>
            <div className="flex items-center space-x-2">
              {isOffline && (
                <Badge variant="destructive" className="text-xs">
                  オフライン
                </Badge>
              )}
              {pwaInstallPrompt && (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => pwaInstallPrompt.showInstallPrompt()}
                >
                  アプリをインストール
                </Button>
              )}
              <Button
                variant="ghost"
                size="icon"
                onClick={() => updateSetting('darkMode', !settings.darkMode)}
              >
                {settings.darkMode ? <Sun className="h-5 w-5" /> : <Moon className="h-5 w-5" />}
              </Button>
              <Button
                variant="ghost"
                size="icon"
                onClick={() => setCurrentView('settings')}
              >
                <Settings className="h-5 w-5" />
              </Button>
            </div>
          </div>
        </div>
      </header>

      <main className="container mx-auto px-4 py-8">
        <div className="mb-8">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
            <Input
              placeholder="作品名や著者名で検索..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10"
            />
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredBooks.map((book) => {
            const bookProgress = progress && progress.bookId === book.id ? progress : { percentage: 0 };
            const isBookFavorite = isFavorite(book.id);

            return (
              <Card key={book.id} className="cursor-pointer hover:shadow-lg transition-shadow">
                <CardHeader>
                  <div className="flex justify-between items-start">
                    <div className="flex-1">
                      <CardTitle className="text-lg">{book.title}</CardTitle>
                      <CardDescription className="text-sm text-muted-foreground">
                        {book.author} • {book.publishedYear}年
                      </CardDescription>
                      {bookProgress.percentage > 0 && (
                        <div className="mt-2">
                          <div className="flex items-center justify-between text-xs text-muted-foreground mb-1">
                            <span>読書進捗</span>
                            <span>{bookProgress.percentage}%</span>
                          </div>
                          <Progress value={bookProgress.percentage} className="h-1" />
                        </div>
                      )}
                    </div>
                    <div className="flex items-center space-x-2">
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={(e) => {
                          e.stopPropagation();
                          toggleFavorite(book.id);
                        }}
                      >
                        <Heart className={`h-4 w-4 ${isBookFavorite ? 'fill-red-500 text-red-500' : ''}`} />
                      </Button>
                      <Badge variant="secondary">{book.category}</Badge>
                    </div>
                  </div>
                </CardHeader>
                <CardContent>
                  <p className="text-sm text-muted-foreground mb-4 line-clamp-3">
                    {book.description}
                  </p>
                  <div className="flex items-center justify-between">
                    <div className="text-xs text-muted-foreground">
                      {book.length} • {book.genre}
                    </div>
                    <Button
                      onClick={() => handleBookSelect(book)}
                      size="sm"
                    >
                      <BookOpen className="h-4 w-4 mr-2" />
                      読む
                    </Button>
                  </div>
                </CardContent>
              </Card>
            );
          })}
        </div>

        {filteredBooks.length === 0 && searchQuery && (
          <div className="text-center py-12">
            <p className="text-muted-foreground">検索結果が見つかりませんでした。</p>
          </div>
        )}
      </main>
    </div>
  )

  const renderReader = () => (
    <div className="min-h-screen bg-background">
      <header className="border-b border-border bg-card sticky top-0 z-10">
        <div className="container mx-auto px-4 py-3">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <Button
                variant="ghost"
                size="icon"
                onClick={() => setCurrentView('home')}
              >
                <ArrowLeft className="h-5 w-5" />
              </Button>
              <div>
                <h1 className="text-lg font-bold text-foreground">{selectedBook?.title}</h1>
                <p className="text-sm text-muted-foreground">{selectedBook?.author}</p>
              </div>
            </div>
            <div className="flex items-center space-x-2">
              {progress && progress.percentage > 0 && (
                <div className="hidden md:flex items-center space-x-2 text-sm text-muted-foreground">
                  <span>{progress.percentage}%</span>
                  <Progress value={progress.percentage} className="w-20 h-2" />
                </div>
              )}
              <ShareBook
                book={selectedBook}
                progress={progress}
                review={reviews.length > 0 ? reviews[0] : null}
              />
              <Button
                variant="ghost"
                size="icon"
                onClick={() => toggleFavorite(selectedBook?.id)}
              >
                <Heart className={`h-5 w-5 ${isFavorite(selectedBook?.id) ? 'fill-red-500 text-red-500' : ''}`} />
              </Button>
              <Button
                variant="ghost"
                size="icon"
                onClick={() => updateSetting('writingMode', settings.writingMode === 'horizontal' ? 'vertical' : 'horizontal')}
              >
                {settings.writingMode === 'horizontal' ? <AlignLeft className="h-5 w-5" /> : <AlignCenter className="h-5 w-5" />}
              </Button>
              <Button
                variant="ghost"
                size="icon"
                onClick={() => updateSetting('darkMode', !settings.darkMode)}
              >
                {settings.darkMode ? <Sun className="h-5 w-5" /> : <Moon className="h-5 w-5" />}
              </Button>
            </div>
          </div>
        </div>
      </header>

      <main className="container mx-auto px-4 py-6">
        {contentLoading ? (
          <div className="flex items-center justify-center py-12">
            <div className="text-center">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
              <p className="text-muted-foreground">本文を読み込み中...</p>
            </div>
          </div>
        ) : (
          <div className="space-y-8">
            <div
              className={`reading-content ${settings.writingMode === 'vertical' ? 'vertical' : 'horizontal'}`}
              dangerouslySetInnerHTML={{ __html: bookContent }}
            />

            {/* レビューセクション */}
            <div className="max-w-4xl mx-auto">
              <Card>
                <CardHeader>
                  <div className="flex items-center justify-between">
                    <div>
                      <CardTitle className="flex items-center space-x-2">
                        <MessageSquare className="h-5 w-5" />
                        <span>レビュー・感想</span>
                      </CardTitle>
                      <CardDescription>
                        この作品についての感想をお聞かせください
                      </CardDescription>
                    </div>
                    <ReviewDialog
                      bookId={selectedBook?.id}
                      bookTitle={selectedBook?.title}
                      onReviewSubmit={addReview}
                    />
                  </div>
                </CardHeader>
                <CardContent>
                  <ReviewList
                    reviews={reviews}
                    onEdit={updateReview}
                    onDelete={deleteReview}
                    canEdit={true}
                  />
                </CardContent>
              </Card>
            </div>
          </div>
        )}
      </main>
    </div>
  )

  const renderSettings = () => (
    <div className="min-h-screen bg-background">
      <header className="border-b border-border bg-card">
        <div className="container mx-auto px-4 py-4">
          <div className="flex items-center space-x-4">
            <Button
              variant="ghost"
              size="icon"
              onClick={() => setCurrentView('home')}
            >
              <ArrowLeft className="h-5 w-5" />
            </Button>
            <h1 className="text-xl font-bold text-foreground">設定</h1>
          </div>
        </div>
      </header>

      <main className="container mx-auto px-4 py-8 max-w-2xl space-y-6">
        {/* 表示設定 */}
        <Card>
          <CardHeader>
            <CardTitle>表示設定</CardTitle>
            <CardDescription>読書体験をカスタマイズできます</CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="flex items-center justify-between">
              <div>
                <h3 className="font-medium">ダークモード</h3>
                <p className="text-sm text-muted-foreground">暗い背景で表示します</p>
              </div>
              <Switch
                checked={settings.darkMode}
                onCheckedChange={(checked) => updateSetting('darkMode', checked)}
              />
            </div>

            <div className="space-y-3">
              <div>
                <h3 className="font-medium mb-2">表示方向</h3>
                <Select
                  value={settings.writingMode}
                  onValueChange={(value) => updateSetting('writingMode', value)}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="horizontal">横書き</SelectItem>
                    <SelectItem value="vertical">縦書き</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div className="space-y-3">
              <div>
                <h3 className="font-medium mb-2">フォントサイズ: {settings.fontSize}px</h3>
                <Slider
                  value={[settings.fontSize]}
                  onValueChange={([value]) => updateSetting('fontSize', value)}
                  min={12}
                  max={24}
                  step={1}
                  className="w-full"
                />
              </div>
            </div>

            <div className="space-y-3">
              <div>
                <h3 className="font-medium mb-2">行間: {settings.lineHeight}</h3>
                <Slider
                  value={[settings.lineHeight]}
                  onValueChange={([value]) => updateSetting('lineHeight', value)}
                  min={1.2}
                  max={2.5}
                  step={0.1}
                  className="w-full"
                />
              </div>
            </div>

            <div className="space-y-3">
              <div>
                <h3 className="font-medium mb-2">フォント</h3>
                <Select
                  value={settings.fontFamily}
                  onValueChange={(value) => updateSetting('fontFamily', value)}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="serif">明朝体</SelectItem>
                    <SelectItem value="sans-serif">ゴシック体</SelectItem>
                    <SelectItem value="monospace">等幅フォント</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div className="space-y-3">
              <div>
                <h3 className="font-medium mb-2">ページ幅</h3>
                <Select
                  value={settings.pageWidth}
                  onValueChange={(value) => updateSetting('pageWidth', value)}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="narrow">狭い</SelectItem>
                    <SelectItem value="normal">標準</SelectItem>
                    <SelectItem value="wide">広い</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* 読書設定 */}
        <Card>
          <CardHeader>
            <CardTitle>読書設定</CardTitle>
            <CardDescription>読書進捗や履歴の管理設定</CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="flex items-center justify-between">
              <div>
                <h3 className="font-medium">自動保存</h3>
                <p className="text-sm text-muted-foreground">読書進捗を自動的に保存します</p>
              </div>
              <Switch
                checked={settings.autoSave}
                onCheckedChange={(checked) => updateSetting('autoSave', checked)}
              />
            </div>

            <div className="space-y-3">
              <div>
                <h3 className="font-medium mb-2">読書速度: {settings.readingSpeed}文字/分</h3>
                <Slider
                  value={[settings.readingSpeed]}
                  onValueChange={([value]) => updateSetting('readingSpeed', value)}
                  min={100}
                  max={800}
                  step={50}
                  className="w-full"
                />
                <p className="text-xs text-muted-foreground mt-1">
                  読書時間の推定に使用されます
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* 実装済み機能 */}
        <Card>
          <CardHeader>
            <CardTitle>実装済み機能</CardTitle>
            <CardDescription>現在利用可能な機能一覧</CardDescription>
          </CardHeader>
          <CardContent>
            <ul className="text-sm space-y-2">
              <li className="flex items-center space-x-2">
                <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                <span>縦書き/横書き表示の切り替え</span>
              </li>
              <li className="flex items-center space-x-2">
                <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                <span>フォントサイズ・行間の調整</span>
              </li>
              <li className="flex items-center space-x-2">
                <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                <span>読書進捗の保存</span>
              </li>
              <li className="flex items-center space-x-2">
                <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                <span>お気に入り機能</span>
              </li>
              <li className="flex items-center space-x-2">
                <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                <span>読書履歴</span>
              </li>
              <li className="flex items-center space-x-2">
                <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                <span>設定の永続化</span>
              </li>
            </ul>
          </CardContent>
        </Card>
      </main>
    </div>
  )

  switch (currentView) {
    case 'reader':
      return renderReader()
    case 'settings':
      return renderSettings()
    default:
      return renderHome()
  }
}

export default App


import{validateFieldsNatively as r,toNestErrors as o}from"@hookform/resolvers";import{TypeCheck as e}from"@sinclair/typebox/compiler";import{Value as s}from"@sinclair/typebox/value";import{appendErrors as t}from"react-hook-form";function a(r,o){const e={};for(;r.length;){const s=r[0],{type:a,message:i,path:n}=s,c=n.substring(1).replace(/\//g,".");if(e[c]||(e[c]={message:i,type:""+a}),o){const r=e[c].types,i=r&&r[""+a];e[c]=t(c,o,e,""+a,i?[].concat(i,s.message):s.message)}r.shift()}return e}function i(t){return async(i,n,c)=>{const l=Array.from(t instanceof e?t.Errors(i):s.Errors(t,i));return c.shouldUseNativeValidation&&r({},c),l.length?{values:{},errors:o(a(l,!c.shouldUseNativeValidation&&"all"===c.criteriaMode),c)}:{errors:{},values:i}}}export{i as typeboxResolver};
//# sourceMappingURL=typebox.modern.mjs.map

var xt=Object.create;var $=Object.defineProperty,jt=Object.defineProperties,Lt=Object.getOwnPropertyDescriptor,Nt=Object.getOwnPropertyDescriptors,$t=Object.getOwnPropertyNames,U=Object.getOwnPropertySymbols,Ft=Object.getPrototypeOf,ot=Object.prototype.hasOwnProperty,bt=Object.prototype.propertyIsEnumerable;var St=(e,n,t)=>n in e?$(e,n,{enumerable:!0,configurable:!0,writable:!0,value:t}):e[n]=t,Pt=(e,n)=>{for(var t in n||(n={}))ot.call(n,t)&&St(e,t,n[t]);if(U)for(var t of U(n))bt.call(n,t)&&St(e,t,n[t]);return e},ht=(e,n)=>jt(e,Nt(n));var _t=(e,n)=>{var t={};for(var i in e)ot.call(e,i)&&n.indexOf(i)<0&&(t[i]=e[i]);if(e!=null&&U)for(var i of U(e))n.indexOf(i)<0&&bt.call(e,i)&&(t[i]=e[i]);return t};var zt=(e,n)=>{for(var t in n)$(e,t,{get:n[t],enumerable:!0})},wt=(e,n,t,i)=>{if(n&&typeof n=="object"||typeof n=="function")for(let d of $t(n))!ot.call(e,d)&&d!==t&&$(e,d,{get:()=>n[d],enumerable:!(i=Lt(n,d))||i.enumerable});return e};var rt=(e,n,t)=>(t=e!=null?xt(Ft(e)):{},wt(n||!e||!e.__esModule?$(t,"default",{value:e,enumerable:!0}):t,e)),Vt=e=>wt($({},"__esModule",{value:!0}),e);var Qt={};zt(Qt,{OTPInput:()=>Dt,OTPInputContext:()=>yt,REGEXP_ONLY_CHARS:()=>Kt,REGEXP_ONLY_DIGITS:()=>qt,REGEXP_ONLY_DIGITS_AND_CHARS:()=>Jt});module.exports=Vt(Qt);var r=rt(require("react"));function Tt(e){let n=setTimeout(e,0),t=setTimeout(e,10),i=setTimeout(e,50);return[n,t,i]}var Z=rt(require("react"));function It(e){let n=Z.useRef();return Z.useEffect(()=>{n.current=e}),n.current}var b=rt(require("react")),Xt=18,Mt=40,Yt=`${Mt}px`,Ut=["[data-lastpass-icon-root]","com-1password-button","[data-dashlanecreated]",'[style$="2147483647 !important;"]'].join(",");function Ct({containerRef:e,inputRef:n,pushPasswordManagerStrategy:t,isFocused:i}){let[d,D]=b.useState(!1),[G,H]=b.useState(!1),[z,W]=b.useState(!1),q=b.useMemo(()=>t==="none"?!1:(t==="increase-width"||t==="experimental-no-flickering")&&d&&G,[d,G,t]),T=b.useCallback(()=>{let m=e.current,h=n.current;if(!m||!h||z||t==="none")return;let a=m,B=a.getBoundingClientRect().left+a.offsetWidth,A=a.getBoundingClientRect().top+a.offsetHeight/2,V=B-Xt,K=A;document.querySelectorAll(Ut).length===0&&document.elementFromPoint(V,K)===m||(D(!0),W(!0))},[e,n,z,t]);return b.useEffect(()=>{let m=e.current;if(!m||t==="none")return;function h(){let A=window.innerWidth-m.getBoundingClientRect().right;H(A>=Mt)}h();let a=setInterval(h,1e3);return()=>{clearInterval(a)}},[e,t]),b.useEffect(()=>{let m=i||document.activeElement===n.current;if(t==="none"||!m)return;let h=setTimeout(T,0),a=setTimeout(T,2e3),B=setTimeout(T,5e3),A=setTimeout(()=>{W(!0)},6e3);return()=>{clearTimeout(h),clearTimeout(a),clearTimeout(B),clearTimeout(A)}},[n,i,t,T]),{hasPWMBadge:d,willPushPWMBadge:q,PWM_BADGE_SPACE_WIDTH:Yt}}var yt=r.createContext({}),Dt=r.forwardRef((A,B)=>{var V=A,{value:e,onChange:n,maxLength:t,textAlign:i="left",pattern:d,placeholder:D,inputMode:G="numeric",onComplete:H,pushPasswordManagerStrategy:z="increase-width",pasteTransformer:W,containerClassName:q,noScriptCSSFallback:T=Zt,render:m,children:h}=V,a=_t(V,["value","onChange","maxLength","textAlign","pattern","placeholder","inputMode","onComplete","pushPasswordManagerStrategy","pasteTransformer","containerClassName","noScriptCSSFallback","render","children"]);var Y,ft,mt,pt,Rt;let[K,st]=r.useState(typeof a.defaultValue=="string"?a.defaultValue:""),l=e!=null?e:K,I=It(l),x=r.useCallback(o=>{n==null||n(o),st(o)},[n]),p=r.useMemo(()=>d?typeof d=="string"?new RegExp(d):d:null,[d]),u=r.useRef(null),J=r.useRef(null),Q=r.useRef({value:l,onChange:x,isIOS:typeof window!="undefined"&&((ft=(Y=window==null?void 0:window.CSS)==null?void 0:Y.supports)==null?void 0:ft.call(Y,"-webkit-touch-callout","none"))}),X=r.useRef({prev:[(mt=u.current)==null?void 0:mt.selectionStart,(pt=u.current)==null?void 0:pt.selectionEnd,(Rt=u.current)==null?void 0:Rt.selectionDirection]});r.useImperativeHandle(B,()=>u.current,[]),r.useEffect(()=>{let o=u.current,s=J.current;if(!o||!s)return;Q.current.value!==o.value&&Q.current.onChange(o.value),X.current.prev=[o.selectionStart,o.selectionEnd,o.selectionDirection];function f(){if(document.activeElement!==o){L(null),N(null);return}let c=o.selectionStart,P=o.selectionEnd,vt=o.selectionDirection,g=o.maxLength,C=o.value,_=X.current.prev,E=-1,S=-1,w;if(C.length!==0&&c!==null&&P!==null){let kt=c===P,Ot=c===C.length&&C.length<g;if(kt&&!Ot){let y=c;if(y===0)E=0,S=1,w="forward";else if(y===g)E=y-1,S=y,w="backward";else if(g>1&&C.length>1){let nt=0;if(_[0]!==null&&_[1]!==null){w=y<_[1]?"backward":"forward";let Gt=_[0]===_[1]&&_[0]<g;w==="backward"&&!Gt&&(nt=-1)}E=nt+y,S=nt+y+1}}E!==-1&&S!==-1&&E!==S&&u.current.setSelectionRange(E,S,w)}let gt=E!==-1?E:c,Et=S!==-1?S:P,At=w!=null?w:vt;L(gt),N(Et),X.current.prev=[gt,Et,At]}if(document.addEventListener("selectionchange",f,{capture:!0}),f(),document.activeElement===o&&tt(!0),!document.getElementById("input-otp-style")){let c=document.createElement("style");if(c.id="input-otp-style",document.head.appendChild(c),c.sheet){let P="background: transparent !important; color: transparent !important; border-color: transparent !important; opacity: 0 !important; box-shadow: none !important; -webkit-box-shadow: none !important; -webkit-text-fill-color: transparent !important;";F(c.sheet,"[data-input-otp]::selection { background: transparent !important; color: transparent !important; }"),F(c.sheet,`[data-input-otp]:autofill { ${P} }`),F(c.sheet,`[data-input-otp]:-webkit-autofill { ${P} }`),F(c.sheet,"@supports (-webkit-touch-callout: none) { [data-input-otp] { letter-spacing: -.6em !important; font-weight: 100 !important; font-stretch: ultra-condensed; font-optical-sizing: none !important; left: -1px !important; right: 1px !important; } }"),F(c.sheet,"[data-input-otp] + * { pointer-events: all !important; }")}}let v=()=>{s&&s.style.setProperty("--root-height",`${o.clientHeight}px`)};v();let R=new ResizeObserver(v);return R.observe(o),()=>{document.removeEventListener("selectionchange",f,{capture:!0}),R.disconnect()}},[]);let[at,ct]=r.useState(!1),[j,tt]=r.useState(!1),[M,L]=r.useState(null),[k,N]=r.useState(null);r.useEffect(()=>{Tt(()=>{var v,R,c,P;(v=u.current)==null||v.dispatchEvent(new Event("input"));let o=(R=u.current)==null?void 0:R.selectionStart,s=(c=u.current)==null?void 0:c.selectionEnd,f=(P=u.current)==null?void 0:P.selectionDirection;o!==null&&s!==null&&(L(o),N(s),X.current.prev=[o,s,f])})},[l,j]),r.useEffect(()=>{I!==void 0&&l!==I&&I.length<t&&l.length===t&&(H==null||H(l))},[t,H,I,l]);let O=Ct({containerRef:J,inputRef:u,pushPasswordManagerStrategy:z,isFocused:j}),it=r.useCallback(o=>{let s=o.currentTarget.value.slice(0,t);if(s.length>0&&p&&!p.test(s)){o.preventDefault();return}typeof I=="string"&&s.length<I.length&&document.dispatchEvent(new Event("selectionchange")),x(s)},[t,x,I,p]),lt=r.useCallback(()=>{var o;if(u.current){let s=Math.min(u.current.value.length,t-1),f=u.current.value.length;(o=u.current)==null||o.setSelectionRange(s,f),L(s),N(f)}tt(!0)},[t]),ut=r.useCallback(o=>{var E,S;let s=u.current;if(!W&&(!Q.current.isIOS||!o.clipboardData||!s))return;let f=o.clipboardData.getData("text/plain"),v=W?W(f):f;o.preventDefault();let R=(E=u.current)==null?void 0:E.selectionStart,c=(S=u.current)==null?void 0:S.selectionEnd,g=(R!==c?l.slice(0,R)+v+l.slice(c):l.slice(0,R)+v+l.slice(R)).slice(0,t);if(g.length>0&&p&&!p.test(g))return;s.value=g,x(g);let C=Math.min(g.length,t-1),_=g.length;s.setSelectionRange(C,_),L(C),N(_)},[t,x,p,l]),Ht=r.useMemo(()=>({position:"relative",cursor:a.disabled?"default":"text",userSelect:"none",WebkitUserSelect:"none",pointerEvents:"none"}),[a.disabled]),dt=r.useMemo(()=>({position:"absolute",inset:0,width:O.willPushPWMBadge?`calc(100% + ${O.PWM_BADGE_SPACE_WIDTH})`:"100%",clipPath:O.willPushPWMBadge?`inset(0 ${O.PWM_BADGE_SPACE_WIDTH} 0 0)`:void 0,height:"100%",display:"flex",textAlign:i,opacity:"1",color:"transparent",pointerEvents:"all",background:"transparent",caretColor:"transparent",border:"0 solid transparent",outline:"0 solid transparent",boxShadow:"none",lineHeight:"1",letterSpacing:"-.5em",fontSize:"var(--root-height)",fontFamily:"monospace",fontVariantNumeric:"tabular-nums"}),[O.PWM_BADGE_SPACE_WIDTH,O.willPushPWMBadge,i]),Wt=r.useMemo(()=>r.createElement("input",ht(Pt({autoComplete:a.autoComplete||"one-time-code"},a),{"data-input-otp":!0,"data-input-otp-placeholder-shown":l.length===0||void 0,"data-input-otp-mss":M,"data-input-otp-mse":k,inputMode:G,pattern:p==null?void 0:p.source,"aria-placeholder":D,style:dt,maxLength:t,value:l,ref:u,onPaste:o=>{var s;ut(o),(s=a.onPaste)==null||s.call(a,o)},onChange:it,onMouseOver:o=>{var s;ct(!0),(s=a.onMouseOver)==null||s.call(a,o)},onMouseLeave:o=>{var s;ct(!1),(s=a.onMouseLeave)==null||s.call(a,o)},onFocus:o=>{var s;lt(),(s=a.onFocus)==null||s.call(a,o)},onBlur:o=>{var s;tt(!1),(s=a.onBlur)==null||s.call(a,o)}})),[it,lt,ut,G,dt,t,k,M,a,p==null?void 0:p.source,l]),et=r.useMemo(()=>({slots:Array.from({length:t}).map((o,s)=>{var c;let f=j&&M!==null&&k!==null&&(M===k&&s===M||s>=M&&s<k),v=l[s]!==void 0?l[s]:null,R=l[0]!==void 0?null:(c=D==null?void 0:D[s])!=null?c:null;return{char:v,placeholderChar:R,isActive:f,hasFakeCaret:f&&v===null}}),isFocused:j,isHovering:!a.disabled&&at}),[j,at,t,k,M,a.disabled,l]),Bt=r.useMemo(()=>m?m(et):r.createElement(yt.Provider,{value:et},h),[h,et,m]);return r.createElement(r.Fragment,null,T!==null&&r.createElement("noscript",null,r.createElement("style",null,T)),r.createElement("div",{ref:J,"data-input-otp-container":!0,style:Ht,className:q},Bt,r.createElement("div",{style:{position:"absolute",inset:0,pointerEvents:"none"}},Wt)))});Dt.displayName="Input";function F(e,n){try{e.insertRule(n)}catch(t){console.error("input-otp could not insert CSS rule:",n)}}var Zt=`
[data-input-otp] {
  --nojs-bg: white !important;
  --nojs-fg: black !important;

  background-color: var(--nojs-bg) !important;
  color: var(--nojs-fg) !important;
  caret-color: var(--nojs-fg) !important;
  letter-spacing: .25em !important;
  text-align: center !important;
  border: 1px solid var(--nojs-fg) !important;
  border-radius: 4px !important;
  width: 100% !important;
}
@media (prefers-color-scheme: dark) {
  [data-input-otp] {
    --nojs-bg: black !important;
    --nojs-fg: white !important;
  }
}`;var qt="^\\d+$",Kt="^[a-zA-Z]+$",Jt="^[a-zA-Z0-9]+$";0&&(module.exports={OTPInput,OTPInputContext,REGEXP_ONLY_CHARS,REGEXP_ONLY_DIGITS,REGEXP_ONLY_DIGITS_AND_CHARS});
//# sourceMappingURL=index.js.map